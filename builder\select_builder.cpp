#include "select_builder.h"

#include <sstream>
#include <format>

namespace database {

SelectBuilder::SelectBuilder()
    : m_isDistinct(false)
    , m_limit(-1)
    , m_offset(-1)
{
}

SelectBuilder& SelectBuilder::select(const std::vector<SqlColumn>& columns) {
    m_columns = columns;
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::selectAll() {
    m_columns.clear();
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::distinct() {
    m_isDistinct = true;
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::from(const SqlTable& table) {
    m_table = table;
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::join(const SqlTable& table, const SqlCondition& condition, SqlJoinType joinType) {
    m_joins.push_back(JoinClause{
        .table = table,
        .condition = condition,
        .joinType = joinType
    });

    addConditionParameters(condition);
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::leftJoin(const SqlTable& table, const SqlCondition& condition) {
    return join(table, condition, SqlJoinType::Left);
}

SelectBuilder& SelectBuilder::rightJoin(const SqlTable& table, const SqlCondition& condition) {
    return join(table, condition, SqlJoinType::Right);
}

void SelectBuilder::addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator) {
    m_whereConditions.push_back(WhereClause{
        .condition = condition,
        .logicalOperator = logicalOperator,
        .isFirst = m_whereConditions.empty()
    });

    addConditionParameters(condition);
    m_sqlDirty = true;
}

SelectBuilder& SelectBuilder::where(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

SelectBuilder& SelectBuilder::where(std::string_view column, std::string_view op, const Variant& value) {
    std::string paramName = generateParamName("where");
    m_parameters[paramName] = value;

    SqlCondition condition(SqlColumn(column), op, paramName);
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

SelectBuilder& SelectBuilder::andWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

SelectBuilder& SelectBuilder::orWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::Or);
    return *this;
}

SelectBuilder& SelectBuilder::groupBy(const std::vector<SqlColumn>& columns) {
    m_groupByColumns = columns;
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::having(const SqlCondition& condition) {
    m_havingCondition = condition;
    addConditionParameters(condition);
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::orderBy(const SqlColumn& column, SqlSortOrder order) {
    m_orderByClauses.push_back(OrderByClause{
        .column = column,
        .order = order
    });

    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::limit(int limit) {
    m_limit = limit;
    m_sqlDirty = true;
    return *this;
}

SelectBuilder& SelectBuilder::offset(int offset) {
    m_offset = offset;
    m_sqlDirty = true;
    return *this;
}

std::string SelectBuilder::build() const {
    if (m_sqlDirty) {
        buildSql();
    }

    return m_cachedSql;
}

void SelectBuilder::buildSql() const {
    std::ostringstream sql;

    // SELECT clause
    sql << "SELECT ";

    if (m_isDistinct) {
        sql << "DISTINCT ";
    }

    if (m_columns.empty()) {
        sql << "*";
    } else {
        sql << joinColumns(std::span(m_columns), ", ");
    }

    // FROM clause
    sql << " FROM ";

    if (m_table.has_value() && !m_table->name().empty()) {
        sql << tableToSql(*m_table);
    } else {
        sql << "*";
    }

    // JOIN clauses
    for (const auto& join : m_joins) {
        if (join.table.name().empty()) {
            continue;  // Skip joins with empty tables
        }

        sql << std::format(" {} {} ON {}",
                           sqlJoinTypeToString(join.joinType),
                           tableToSql(join.table),
                           conditionToSql(join.condition));
    }

    // WHERE clause
    if (!m_whereConditions.empty()) {
        sql << " WHERE ";

        bool first = true;
        for (const auto& where : m_whereConditions) {
            if (where.condition.isEmpty()) {
                continue;  // Skip empty conditions
            }

            if (!first) {
                sql << std::format(" {} ", sqlLogicalOperatorToString(where.logicalOperator));
            }

            sql << conditionToSql(where.condition);

            first = false;
        }
    }

    // GROUP BY clause
    if (!m_groupByColumns.empty()) {
        bool hasValidColumns = false;
        for (const auto& column : m_groupByColumns) {
            if (!column.name().empty()) {
                hasValidColumns = true;
                break;
            }
        }

        if (hasValidColumns) {
            sql << " GROUP BY " << joinColumns(std::span(m_groupByColumns), ", ");
        }
    }

    // HAVING clause
    if (m_havingCondition.has_value() && !m_havingCondition->isEmpty()) {
        sql << " HAVING " << conditionToSql(*m_havingCondition);
    }

    // ORDER BY clause
    if (!m_orderByClauses.empty()) {
        bool hasValidColumns = false;
        for (const auto& orderBy : m_orderByClauses) {
            if (!orderBy.column.name().empty()) {
                hasValidColumns = true;
                break;
            }
        }

        if (hasValidColumns) {
            sql << " ORDER BY ";

            bool first = true;
            for (const auto& orderBy : m_orderByClauses) {
                if (orderBy.column.name().empty()) {
                    continue;  // Skip empty columns
                }

                if (!first) {
                    sql << ", ";
                }

                sql << columnToSql(orderBy.column);

                if (orderBy.order == SqlSortOrder::Descending) {
                    sql << " DESC";
                }

                first = false;
            }
        }
    }

    // LIMIT and OFFSET clauses
    if (m_limit >= 0) {
        sql << std::format(" LIMIT {}", m_limit);
    }

    if (m_offset >= 0) {
        sql << std::format(" OFFSET {}", m_offset);
    }

    m_cachedSql = sql.str();
    m_sqlDirty = false;
}

} // namespace database
