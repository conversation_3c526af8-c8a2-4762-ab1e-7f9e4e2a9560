#include "delete_builder.h"

#include <sstream>
#include <format>

namespace database {

DeleteBuilder::DeleteBuilder() = default;

DeleteBuilder::DeleteBuilder(const SqlTable& table) {
    m_table = table;
}

DeleteBuilder& DeleteBuilder::from(const SqlTable& table) {
    m_table = table;
    m_sqlDirty = true;
    return *this;
}

void DeleteBuilder::addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator) {
    m_whereConditions.push_back(WhereClause{
        .condition = condition,
        .logicalOperator = logicalOperator,
        .isFirst = m_whereConditions.empty()
    });

    addConditionParameters(condition);
    m_sqlDirty = true;
}

DeleteBuilder& DeleteBuilder::where(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

DeleteBuilder& DeleteBuilder::andWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

DeleteBuilder& DeleteBuilder::orWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::Or);
    return *this;
}

std::string DeleteBuilder::build() const {
    if (m_sqlDirty) {
        buildSql();
    }

    return m_cachedSql;
}

void DeleteBuilder::buildSql() const {
    std::ostringstream sql;

    // DELETE FROM clause
    sql << "DELETE FROM ";

    if (m_table.has_value()) {
        sql << m_table->name();
    } else {
        throw std::invalid_argument("SqlTable is required for DELETE queries");
    }

    // WHERE clause
    if (!m_whereConditions.empty()) {
        sql << " WHERE ";

        bool first = true;
        for (const auto& where : m_whereConditions) {
            if (!first) {
                sql << std::format(" {} ", sqlLogicalOperatorToString(where.logicalOperator));
            }

            sql << conditionToSql(where.condition);

            first = false;
        }
    }

    m_cachedSql = sql.str();
    m_sqlDirty = false;
}

} // namespace database
