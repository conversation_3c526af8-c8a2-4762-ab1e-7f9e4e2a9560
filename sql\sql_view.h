#ifndef DATABASE_SQL_VIEW_H
#define DATABASE_SQL_VIEW_H

#include <memory>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>
#include <algorithm>
#include <type_traits>
#include <functional>
#include <string>
#include <string_view>

#include "sql_object.h"
#include "sql_types.h"

namespace database {

// Forward declarations
class SqlViewPrivate;
class SqlTable;
class SqlColumn;
class SqlRow;
class SqlDatabase;

/**
 * @brief Type trait for view-like objects
 */
template<typename T>
struct is_view_like {
private:
    template<typename U>
    static auto test(int) -> decltype(
        std::declval<const U&>().definition(),
        std::declval<const U&>().isUpdatable(),
        std::declval<const U&>().isMaterialized(),
        std::true_type{}
    );

    template<typename>
    static std::false_type test(...);

public:
    static constexpr bool value = decltype(test<T>(0))::value;
};

template<typename T>
constexpr bool is_view_like_v = is_view_like<T>::value;

/**
 * @brief SQL view object with three-tier architecture
 *
 * This class represents a database view and implements the three-tier architecture:
 * - Tier 1 (Core Concepts): Lightweight base with essential properties (inherited from SqlObject)
 * - Tier 2 (Extended Metadata): Optional comprehensive metadata (SqlViewMetadata)
 * - Tier 3 (Database Interaction): Actual database operations via PIMPL (SqlViewPrivate)
 *
 * The three-tier implementation is hidden from the public API to provide clean, intuitive interfaces.
 */
class SqlView final : public SqlObject {
public:

    /**
     * @brief Enhanced metadata for SQL view objects with modern C++ optimizations
     *
     * This struct contains comprehensive descriptive information about database views
     * with enhanced dependency analysis and performance optimization features.
     */
    struct SqlViewMetadata {
        std::string schema;                                 ///< Schema name
        std::string definition;                             ///< View definition (SELECT statement)
        std::string comment;                                ///< View comment/description
        std::vector<std::string> referencedTables;         ///< Tables referenced by the view
        std::vector<std::string> columns;                   ///< View columns
        bool isUpdatable = false;                           ///< Is updatable view
        bool isMaterialized = false;                        ///< Is materialized view
        bool withCheckOption = false;                       ///< Has WITH CHECK OPTION
        std::string checkOption;                            ///< Check option type (LOCAL/CASCADED)
        std::string securityType;                           ///< Security type (DEFINER/INVOKER)
        std::string definer;                                ///< View definer
        std::optional<std::chrono::system_clock::time_point> createdTime; ///< Creation time
        std::optional<std::chrono::system_clock::time_point> lastRefreshTime; ///< Last refresh time (for materialized views)
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

        // Enhanced constructors with perfect forwarding
        SqlViewMetadata() = default;

        template<typename Schema, typename Definition, typename Comment = std::string>
        SqlViewMetadata(Schema&& schema, Definition&& definition, Comment&& comment = {})
            : schema(std::forward<Schema>(schema))
            , definition(std::forward<Definition>(definition))
            , comment(std::forward<Comment>(comment)) {}

        // Enhanced dependency analysis
        [[nodiscard]] std::vector<std::string> extractTableDependencies() const {
            std::vector<std::string> dependencies;
            // Parse definition to extract table names
            // This is a simplified implementation - real implementation would use SQL parser
            std::string upperDef = definition;
            std::transform(upperDef.begin(), upperDef.end(), upperDef.begin(), ::toupper);

            size_t fromPos = upperDef.find("FROM");
            if (fromPos != std::string::npos) {
                // Extract table names after FROM clause
                // This is a basic implementation - production code would need proper SQL parsing
                size_t start = fromPos + 4;
                size_t end = upperDef.find_first_of(" \t\n\r;", start);
                if (end != std::string::npos) {
                    std::string tableName = definition.substr(start, end - start);
                    // Trim whitespace
                    tableName.erase(0, tableName.find_first_not_of(" \t\n\r"));
                    tableName.erase(tableName.find_last_not_of(" \t\n\r") + 1);
                    if (!tableName.empty()) {
                        dependencies.push_back(tableName);
                    }
                }
            }
            return dependencies;
        }

        [[nodiscard]] std::vector<std::string> extractColumnDependencies() const {
            // Extract column references from the view definition
            // This would require proper SQL parsing in production
            return columns; // Simplified implementation
        }

        [[nodiscard]] bool dependsOnTable(std::string_view tableName) const noexcept {
            return std::find(referencedTables.begin(), referencedTables.end(), tableName) != referencedTables.end();
        }

        // View type utilities
        [[nodiscard]] bool isSimpleView() const noexcept {
            return !isMaterialized && referencedTables.size() == 1;
        }

        [[nodiscard]] bool isComplexView() const noexcept {
            return referencedTables.size() > 1 || definition.find("JOIN") != std::string::npos;
        }

        // Security and permissions
        [[nodiscard]] bool hasSecurityDefiner() const noexcept {
            return securityType == "DEFINER";
        }

        [[nodiscard]] bool hasSecurityInvoker() const noexcept {
            return securityType == "INVOKER";
        }

        // Materialized view utilities
        [[nodiscard]] bool needsRefresh() const noexcept {
            if (!isMaterialized) return false;
            if (!lastRefreshTime) return true;

            // Check if refresh is needed based on some criteria
            auto now = std::chrono::system_clock::now();
            auto timeSinceRefresh = now - *lastRefreshTime;
            return timeSinceRefresh > std::chrono::hours(24); // Example: refresh daily
        }

        // Column management utilities
        [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept {
            return std::find(columns.begin(), columns.end(), columnName) != columns.end();
        }

        template<typename ColumnRange>
        void setColumns(ColumnRange&& columnRange) {
            columns.clear();
            if constexpr (std::is_same_v<std::decay_t<ColumnRange>, std::vector<std::string>>) {
                columns.reserve(columnRange.size());
            }
            for (auto&& column : columnRange) {
                columns.emplace_back(std::forward<decltype(column)>(column));
            }
        }
    };


    // Enhanced constructors with modern C++ features
    SqlView() noexcept;
    explicit SqlView(std::string_view name) noexcept;
    SqlView(std::string_view name, std::string_view definition);
    SqlView(std::string_view name, SqlViewMetadata metadata);
    SqlView(std::string_view name, SqlViewMetadata metadata, std::shared_ptr<SqlDatabase> database);

    // Template constructor for type-safe view creation
    template<typename Definition, typename = std::enable_if_t<std::is_convertible_v<Definition, std::string>>>
    SqlView(std::string_view name, Definition&& definition, bool materialized = false)
        : SqlView(name) {
        if (auto meta = metadata()) {
            meta->definition = std::forward<Definition>(definition);
            meta->isMaterialized = materialized;
        }
    }

    ~SqlView() override;

    // Enhanced copy/move operations with optimizations
    SqlView(const SqlView& other);
    SqlView(SqlView&& other) noexcept;
    SqlView& operator=(const SqlView& other);
    SqlView& operator=(SqlView&& other) noexcept;

    //----------------------------------------------------------------------
    // Enhanced Static Factory Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlView fromDatabase(std::shared_ptr<SqlDatabase> database,
                                             const std::string& viewName,
                                             const std::string& schema = "",
                                             bool loadMetadata = true);

    // Modern factory methods with perfect forwarding
    template<typename... Args>
    [[nodiscard]] static SqlView create(std::string_view name, std::string_view definition, Args&&... args) {
        SqlView view(name, definition);
        if constexpr (sizeof...(args) > 0) {
            auto meta = view.metadata();
            if (meta) {
                // Apply additional metadata using fold expressions
                (view.applyMetadataArg(std::forward<Args>(args)), ...);
            }
        }
        return view;
    }

    // Utility factory methods for common view types
    [[nodiscard]] static SqlView simple(std::string_view name, std::string_view definition) {
        auto view = SqlView(name, definition);
        if (view.metadata()) {
            view.metadata()->isUpdatable = true; // Simple views are often updatable
        }
        return view;
    }

    [[nodiscard]] static SqlView materialized(std::string_view name, std::string_view definition) {
        auto view = SqlView(name, definition, true);
        if (view.metadata()) {
            view.metadata()->isMaterialized = true;
        }
        return view;
    }

    [[nodiscard]] static SqlView withCheckOption(std::string_view name, std::string_view definition,
                                                 std::string_view checkType = "CASCADED") {
        auto view = SqlView(name, definition);
        if (view.metadata()) {
            view.metadata()->withCheckOption = true;
            view.metadata()->checkOption = checkType;
        }
        return view;
    }

    [[nodiscard]] static SqlView securityDefiner(std::string_view name, std::string_view definition,
                                                 std::string_view definer) {
        auto view = SqlView(name, definition);
        if (view.metadata()) {
            view.metadata()->securityType = "DEFINER";
            view.metadata()->definer = definer;
        }
        return view;
    }

    // Tier 2 - Extended Metadata Operations

    //----------------------------------------------------------------------
    // Enhanced Metadata Management with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] SqlViewMetadata* metadata() const noexcept;
    SqlView& setMetadata(const SqlViewMetadata& metadata);
    [[nodiscard]] bool hasMetadata() const noexcept override;
    bool loadMetadata() override;
    bool refreshMetadata() override;

    // Core properties with optimized accessors and fluent interface
    [[nodiscard]] std::string_view schema() const noexcept;
    SqlView& setSchema(std::string_view schema);

    [[nodiscard]] std::string_view definition() const noexcept;
    SqlView& setDefinition(std::string_view definition);

    [[nodiscard]] std::string_view comment() const noexcept;
    SqlView& setComment(std::string_view comment);

    // Enhanced dependency management
    [[nodiscard]] std::vector<std::string> referencedTables() const noexcept;

    template<typename TableRange>
    SqlView& setReferencedTables(TableRange&& tableRange) {
        if (auto meta = metadata()) {
            meta->referencedTables.clear();
            if constexpr (std::is_same_v<std::decay_t<TableRange>, std::vector<std::string>>) {
                meta->referencedTables.reserve(tableRange.size());
            }
            for (auto&& table : tableRange) {
                meta->referencedTables.emplace_back(std::forward<decltype(table)>(table));
            }
        }
        return *this;
    }

    SqlView& addReferencedTable(std::string_view tableName);
    SqlView& removeReferencedTable(std::string_view tableName);
    [[nodiscard]] bool dependsOnTable(std::string_view tableName) const noexcept;

    // Enhanced column management
    [[nodiscard]] std::vector<std::string> columnNames() const noexcept;

    template<typename ColumnRange>
    SqlView& setColumnNames(ColumnRange&& columnRange) {
        if (auto meta = metadata()) {
            meta->setColumns(std::forward<ColumnRange>(columnRange));
        }
        return *this;
    }

    SqlView& addColumn(std::string_view columnName);
    SqlView& removeColumn(std::string_view columnName);
    [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept;
    [[nodiscard]] size_t columnCount() const noexcept;

    // Enhanced boolean properties with fluent interface
    [[nodiscard]] bool isUpdatable() const noexcept;
    SqlView& setUpdatable(bool updatable = true) noexcept;
    SqlView& updatable() noexcept { return setUpdatable(true); }
    SqlView& readOnly() noexcept { return setUpdatable(false); }

    [[nodiscard]] bool isMaterialized() const noexcept;
    SqlView& setMaterialized(bool materialized = true) noexcept;
    SqlView& materialized() noexcept { return setMaterialized(true); }

    [[nodiscard]] bool hasCheckOption() const noexcept;
    SqlView& setCheckOption(bool checkOption = true) noexcept;
    SqlView& withCheckOption(std::string_view type = "CASCADED");

    [[nodiscard]] std::string_view checkOptionType() const noexcept;
    SqlView& setCheckOptionType(std::string_view type);

    // Enhanced security management
    [[nodiscard]] std::string_view securityType() const noexcept;
    SqlView& setSecurityType(std::string_view type);
    SqlView& securityDefiner(std::string_view definer);
    SqlView& securityInvoker();

    [[nodiscard]] std::string_view definer() const noexcept;
    SqlView& setDefiner(std::string_view definer);

    // Enhanced view analysis
    [[nodiscard]] std::vector<std::string> extractTableDependencies() const;
    [[nodiscard]] std::vector<std::string> extractColumnDependencies() const;
    [[nodiscard]] std::vector<SqlTable> getDependentTables() const;
    [[nodiscard]] std::vector<SqlView> getDependentViews() const;
    [[nodiscard]] bool hasCyclicDependencies() const;

    // Materialized view specific operations
    enum class RefreshStrategy {
        Complete,
        Incremental,
        Concurrent
    };

    bool refresh() const;
    bool refreshConcurrently() const;
    bool refresh(RefreshStrategy strategy) const;
    [[nodiscard]] std::chrono::system_clock::time_point lastRefreshTime() const;
    [[nodiscard]] bool needsRefresh() const;

    //----------------------------------------------------------------------
    // Enhanced Database Interaction Operations
    //----------------------------------------------------------------------
    [[nodiscard]] bool isConnected() const noexcept;
    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    void setDatabase(std::shared_ptr<SqlDatabase> database) override;

    // Database operations with enhanced error handling
    [[nodiscard]] bool exists() const;
    bool create(bool orReplace = false);
    bool drop(bool ifExists = true);
    bool rename(std::string_view newName);

    // Enhanced view validation
    [[nodiscard]] bool isDefinitionValid() const;
    [[nodiscard]] std::vector<std::string> getValidationErrors() const;
    [[nodiscard]] std::vector<std::string> dependencies() const;

    /**
     * @brief Get list of columns from database
     * @return Vector of column objects
     */
    [[nodiscard]] std::vector<SqlColumn> columns() const;

    /**
     * @brief Get column by name from database
     * @param columnName Column name
     * @return Column object, or invalid column if not found
     */
    [[nodiscard]] SqlColumn column(const std::string& columnName) const;

    /**
     * @brief Get row count from view
     * @return Number of rows, or 0 if error or not connected
     */
    [[nodiscard]] size_t rowCount() const;

    /**
     * @brief Select all rows from view
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> selectAll() const;

    /**
     * @brief Select rows with condition
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> select(const std::string& whereClause,
                                            const std::vector<Data>& parameters = {}) const;

    /**
     * @brief Select rows with limit and offset
     * @param limit Maximum number of rows
     * @param offset Number of rows to skip
     * @return Vector of row objects
     */
    [[nodiscard]] std::vector<SqlRow> select(size_t limit, size_t offset = 0) const;

    /**
     * @brief Insert row into view (if updatable)
     * @param row Row to insert
     * @return True if successful, false otherwise
     */
    bool insert(const SqlRow& row);

    /**
     * @brief Update rows in view (if updatable)
     * @param row Row with updated values
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Number of affected rows
     */
    size_t update(const SqlRow& row, const std::string& whereClause,
                  const std::vector<Data>& parameters = {});

    /**
     * @brief Delete rows from view (if updatable)
     * @param whereClause WHERE clause condition
     * @param parameters Parameter values for prepared statement
     * @return Number of affected rows
     */
    size_t deleteRows(const std::string& whereClause,
                     const std::vector<Data>& parameters = {});

    /**
     * @brief Analyze view dependencies
     * @return List of dependent objects
     */
    [[nodiscard]] std::vector<std::string> dependencies() const;

    /**
     * @brief Check if view definition is valid
     * @return True if valid, false otherwise
     */
    [[nodiscard]] bool isDefinitionValid() const;

    /**
     * @brief Refresh metadata from database
     * @return True if successful, false otherwise
     */
    bool refreshMetadata();

    /**
     * @brief Execute custom SQL statement on this view
     * @param sql SQL statement
     * @param parameters Parameter values for prepared statement
     * @return True if successful, false otherwise
     */
    bool execute(const std::string& sql, const std::vector<Data>& parameters = {});

    // Utility methods

    //----------------------------------------------------------------------
    // Enhanced SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    // Enhanced SQL generation methods
    [[nodiscard]] std::string createStatement(bool orReplace = false) const;
    [[nodiscard]] std::string dropStatement(bool ifExists = true) const;
    [[nodiscard]] std::string renameStatement(std::string_view newName) const;
    [[nodiscard]] std::string refreshStatement(RefreshStrategy strategy = RefreshStrategy::Complete) const;

private:
    // Helper methods for metadata management
    template<typename T>
    void applyMetadataArg(T&& arg) {
        if constexpr (std::is_same_v<std::decay_t<T>, bool>) {
            setMaterialized(std::forward<T>(arg));
        } else if constexpr (std::is_same_v<std::decay_t<T>, std::string>) {
            setCheckOptionType(std::forward<T>(arg));
        } else if constexpr (std::is_same_v<std::decay_t<T>, std::vector<std::string>>) {
            setReferencedTables(std::forward<T>(arg));
        }
        // Add more metadata argument types as needed
    }

    // Enhanced member variables with better encapsulation
    mutable std::shared_ptr<SqlViewMetadata> m_metadata;
    std::shared_ptr<SqlViewPrivate> d_ptr;
};

} // namespace database

// Enhanced hash specialization for SqlView with modern C++ features
namespace std {
template<>
struct hash<database::SqlView> {
    [[nodiscard]] size_t operator()(const database::SqlView& view) const noexcept {
        // Use the base SqlObject hash combined with view-specific information
        size_t h1 = view.hash();

        // Hash the schema name if available
        size_t h2 = 0;
        try {
            auto schema = view.schema();
            if (!schema.empty()) {
                h2 = std::hash<std::string_view>{}(schema);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the materialized flag and column count for additional uniqueness
        size_t h3 = view.isMaterialized() ? 1 : 0;
        size_t h4 = view.columnCount();

        return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3);
    }
};
} // namespace std

#endif // DATABASE_SQL_VIEW_H
