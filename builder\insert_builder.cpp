#include "insert_builder.h"

#include <sstream>
#include <format>

namespace database {

InsertBuilder::InsertBuilder()
    : m_useColumnValues(false)
{
}

InsertBuilder& InsertBuilder::into(const SqlTable& table) {
    m_table = table;
    m_sqlDirty = true;
    return *this;
}

InsertBuilder& InsertBuilder::columns(std::span<const SqlColumn> columns) {
    m_columns.assign(columns.begin(), columns.end());
    m_sqlDirty = true;
    return *this;
}

// InsertBuilder& InsertBuilder::values(const std::vector<Variant>& values) {
//     // Check if the number of values matches the number of columns
//     if (!m_columns.empty() && values.size() != m_columns.size()) {
//         throw std::invalid_argument("Number of values does not match number of columns");
//     }

//     m_valuesBatch.push_back(values);
//     m_useColumnValues = false;
//     m_sqlDirty = true;
//     return *this;
// }

InsertBuilder& InsertBuilder::set(const SqlColumn& column, const Variant& value) {
    m_columnValues[column] = value;
    m_useColumnValues = true;
    m_sqlDirty = true;
    return *this;
}

InsertBuilder& InsertBuilder::setRow(const SqlRow& row) {
    for (const auto& [name, value] : row.values()) {
        // Create a column from the column name
        m_columnValues[SqlColumn(name)] = value;
    }
    m_useColumnValues = true;
    m_sqlDirty = true;
    return *this;
}

InsertBuilder& InsertBuilder::addBatch(const std::vector<Variant>& values) {
    // Check if the number of values matches the number of columns
    if (!m_columns.empty() && values.size() != m_columns.size()) {
        throw std::invalid_argument("Number of values does not match number of columns");
    }

    std::vector<Variant> valueVec(values.begin(), values.end());
    m_valuesBatch.push_back(valueVec);
    m_sqlDirty = true;
    return *this;
}

InsertBuilder& InsertBuilder::addBatch(std::span<const Variant> values) {
    // Check if the number of values matches the number of columns
    if (!m_columns.empty() && values.size() != m_columns.size()) {
        throw std::invalid_argument("Number of values does not match number of columns");
    }

    std::vector<Variant> valueVec(values.begin(), values.end());
    m_valuesBatch.push_back(std::move(valueVec));
    m_sqlDirty = true;
    return *this;
}

InsertBuilder& InsertBuilder::addBatchRow(const SqlRow& row) {
    std::vector<Variant> values;
    values.reserve(m_columns.size());

    for (const auto& column : m_columns) {
        values.push_back(row.value(column.name()));
    }

    m_valuesBatch.push_back(std::move(values));
    m_sqlDirty = true;
    return *this;
}

std::string InsertBuilder::build() const {
    if (m_sqlDirty) {
        buildSql();
    }

    return m_cachedSql;
}

void InsertBuilder::buildSql() const {
    std::ostringstream sql;

    // INSERT INTO clause
    sql << "INSERT INTO ";

    if (m_table.has_value()) {
        sql << m_table->name();
    } else {
        throw std::invalid_argument("SqlTable is required for INSERT queries");
    }

    // Columns
    if (m_useColumnValues) {
        // Use column values
        sql << " (";

        bool first = true;
        for (const auto& [column, _] : m_columnValues) {
            if (!first) {
                sql << ", ";
            }

            sql << column.name();
            first = false;
        }

        sql << ") VALUES (";

        first = true;
        for (const auto& [column, value] : m_columnValues) {
            if (!first) {
                sql << ", ";
            }

            std::string paramName = generateParamName("insert");
            m_parameters[paramName] = value;
            sql << std::format(":{}", paramName);

            first = false;
        }

        sql << ")";
    } else {
        // Use columns and values
        if (!m_columns.empty()) {
            sql << " (";

            bool first = true;
            for (const auto& column : m_columns) {
                if (!first) {
                    sql << ", ";
                }

                sql << column.name();
                first = false;
            }

            sql << ")";
        }

        // Values
        if (!m_valuesBatch.empty()) {
            sql << " VALUES ";

            bool firstBatch = true;
            for (size_t batchIndex = 0; batchIndex < m_valuesBatch.size(); ++batchIndex) {
                const auto& batch = m_valuesBatch[batchIndex];

                if (!firstBatch) {
                    sql << ", ";
                }

                sql << "(";

                bool firstValue = true;
                for (size_t i = 0; i < batch.size(); ++i) {
                    if (!firstValue) {
                        sql << ", ";
                    }

                    std::string paramName = generateParamName(std::format("insert_{}_", batchIndex));
                    m_parameters[paramName] = batch[i];
                    sql << std::format(":{}", paramName);

                    firstValue = false;
                }

                sql << ")";
                firstBatch = false;
            }
        }
    }

    m_cachedSql = sql.str();
    m_sqlDirty = false;
}

SqlQuery InsertBuilder::toQuery(SqlDatabase& database) const {
    // For batch inserts, we need to handle them differently
    if (!m_useColumnValues && m_valuesBatch.size() > 1) {
        // Create a query with just the first batch
        InsertBuilder singleBatch;

        if (m_table.has_value()) {
            singleBatch.into(*m_table);
        }

        singleBatch.columns(std::span(m_columns));
        if (!m_valuesBatch.empty()) {
            singleBatch.addBatch(std::span(m_valuesBatch[0]));
        }

        SqlQuery query = singleBatch.toQuery(database);

        // Add the remaining batches
        for (size_t i = 1; i < m_valuesBatch.size(); ++i) {
            // Clear previous bindings
            query.clearBindings();

            // Bind new values
            for (size_t j = 0; j < m_valuesBatch[i].size(); ++j) {
                query.bind(static_cast<int>(j + 1), m_valuesBatch[i][j]);
            }

            // Add to batch
            query.addBatch();
        }

        return query;
    }

    // For single inserts, use the base implementation
    return SqlBuilder::toQuery(database);
}

SqlQuery InsertBuilder::execute(SqlDatabase& database) const {
    SqlQuery query = toQuery(database);

    // For batch inserts, execute as batch
    if (!m_useColumnValues && m_valuesBatch.size() > 1) {
        query.executeBatch();
    } else {
        query.execute();
    }

    return query;
}

} // namespace database
