#include "sql_object.h"

namespace database {

SqlObject::SqlObject(std::string_view name)
    : m_name(name), m_type(SqlObjectType::Unknown) {
}

SqlObject::SqlObject(std::string_view name, SqlObjectType type)
    : m_name(name), m_type(type) {
}

std::string_view SqlObject::name() const noexcept {
    return m_name;
}

void SqlObject::setName(std::string_view name) {
    if (m_name != name) {
        m_name = name;
    }
}

} // namespace database
