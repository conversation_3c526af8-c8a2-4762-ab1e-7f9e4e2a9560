# Implementation Guide for Remaining SqlObject Subclasses

## Overview
This guide provides detailed implementation instructions for optimizing the remaining SqlObject subclasses: SqlIndex, SqlRow, and SqlView.

## 1. SqlIndex Optimization

### 1.1 Enhanced Metadata Structure
```cpp
struct SqlIndexMetadata {
    // Enhanced type checking utilities
    [[nodiscard]] bool isUniqueIndex() const noexcept {
        return indexType == SqlIndexType::Unique || indexType == SqlIndexType::Primary;
    }
    
    [[nodiscard]] bool isClusteredIndex() const noexcept {
        return indexType == SqlIndexType::Clustered;
    }
    
    // Column management with modern C++
    template<typename ColumnRange>
    void setColumns(ColumnRange&& columnRange) {
        columns.clear();
        sortOrders.clear();
        
        for (auto&& column : columnRange) {
            columns.emplace_back(std::forward<decltype(column)>(column));
            sortOrders.emplace_back(SqlSortOrder::Ascending); // Default
        }
    }
};
```

### 1.2 Factory Methods
```cpp
// Utility factory methods for common index types
[[nodiscard]] static SqlIndex primaryKey(std::string_view tableName, 
                                        const std::vector<std::string>& columns);
[[nodiscard]] static SqlIndex unique(std::string_view name, std::string_view tableName,
                                   const std::vector<std::string>& columns);
[[nodiscard]] static SqlIndex clustered(std::string_view name, std::string_view tableName,
                                       const std::vector<std::string>& columns);
```

### 1.3 Enhanced Column Management
```cpp
// Template-based column management
template<typename Container>
SqlIndex& setColumns(const Container& columnNames) {
    if (auto meta = metadata()) {
        meta->setColumns(columnNames);
    }
    return *this;
}

// Fluent interface for sort orders
SqlIndex& setSortOrder(size_t columnIndex, SqlSortOrder order);
SqlIndex& ascending(size_t columnIndex) { return setSortOrder(columnIndex, SqlSortOrder::Ascending); }
SqlIndex& descending(size_t columnIndex) { return setSortOrder(columnIndex, SqlSortOrder::Descending); }
```

## 2. SqlRow Optimization

### 2.1 Enhanced Metadata Structure
```cpp
struct SqlRowMetadata {
    // Enhanced validation utilities
    [[nodiscard]] bool isValidForTable(const SqlTable& table) const;
    [[nodiscard]] std::vector<std::string> getValidationErrors(const SqlTable& table) const;
    
    // Change tracking
    void markDirty(std::string_view columnName) {
        isDirty = true;
        dirtyColumns.insert(std::string(columnName));
    }
    
    void clearDirty() {
        isDirty = false;
        dirtyColumns.clear();
    }
    
private:
    std::unordered_set<std::string> dirtyColumns;
};
```

### 2.2 Type-Safe Value Access
```cpp
// Template-based value access with type safety
template<typename T>
[[nodiscard]] std::optional<T> getValue(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        try {
            return it->second.get<T>();
        } catch (...) {
            return std::nullopt;
        }
    }
    return std::nullopt;
}

template<typename T>
SqlRow& setValue(std::string_view columnName, T&& value) {
    static_assert(std::is_convertible_v<T, Data>, "Value must be convertible to Data");
    m_values[std::string(columnName)] = Data(std::forward<T>(value));
    if (auto meta = metadata()) {
        meta->markDirty(columnName);
    }
    return *this;
}
```

### 2.3 Enhanced Validation
```cpp
// Comprehensive validation with detailed error reporting
[[nodiscard]] bool isValid() const override;
[[nodiscard]] std::vector<std::string> validationErrors() const;
[[nodiscard]] bool isColumnValid(std::string_view columnName) const;

// Type-specific validation
[[nodiscard]] bool validateAgainstTable(const SqlTable& table) const;
[[nodiscard]] bool validateConstraints() const;
[[nodiscard]] bool validateDataTypes() const;
```

## 3. SqlView Optimization

### 3.1 Enhanced Metadata Structure
```cpp
struct SqlViewMetadata {
    // Enhanced dependency analysis
    [[nodiscard]] std::vector<std::string> extractTableDependencies() const;
    [[nodiscard]] std::vector<std::string> extractColumnDependencies() const;
    [[nodiscard]] bool dependsOnTable(std::string_view tableName) const;
    
    // View type utilities
    [[nodiscard]] bool isSimpleView() const {
        return !isMaterialized && referencedTables.size() == 1;
    }
    
    [[nodiscard]] bool isComplexView() const {
        return referencedTables.size() > 1 || definition.find("JOIN") != std::string::npos;
    }
    
    // Security and permissions
    [[nodiscard]] bool hasSecurityDefiner() const {
        return securityType == "DEFINER";
    }
};
```

### 3.2 Enhanced Query Analysis
```cpp
// Advanced view analysis
[[nodiscard]] bool isUpdatable() const override;
[[nodiscard]] std::vector<std::string> getUpdatableColumns() const;
[[nodiscard]] std::string getBaseTable() const; // For simple views
[[nodiscard]] std::vector<std::string> getJoinedTables() const;

// Dependency management
[[nodiscard]] std::vector<SqlTable> getDependentTables() const;
[[nodiscard]] std::vector<SqlView> getDependentViews() const;
[[nodiscard]] bool hasCyclicDependencies() const;
```

### 3.3 Materialized View Support
```cpp
// Materialized view specific operations
bool refresh() override;
bool refreshConcurrently();
[[nodiscard]] std::chrono::system_clock::time_point lastRefreshTime() const;
[[nodiscard]] bool needsRefresh() const;

// Refresh strategies
enum class RefreshStrategy {
    Complete,
    Incremental,
    Concurrent
};

bool refresh(RefreshStrategy strategy);
```

## 4. Common Optimization Patterns

### 4.1 Metadata Management Template
```cpp
template<typename MetadataType>
class MetadataManager {
public:
    [[nodiscard]] MetadataType* metadata() const noexcept {
        if (!m_metadata) {
            m_metadata = std::make_shared<MetadataType>();
        }
        return m_metadata.get();
    }
    
    void setMetadata(const MetadataType& metadata) {
        m_metadata = std::make_shared<MetadataType>(metadata);
    }
    
    [[nodiscard]] bool hasMetadata() const noexcept override {
        return m_metadata != nullptr;
    }

private:
    mutable std::shared_ptr<MetadataType> m_metadata;
};
```

### 4.2 Builder Integration Template
```cpp
template<typename BuilderType>
class BuilderIntegration {
public:
    template<typename... Args>
    [[nodiscard]] BuilderType createBuilder(Args&&... args) const {
        return BuilderType(*static_cast<const DerivedType*>(this), 
                          std::forward<Args>(args)...);
    }
};
```

### 4.3 Database Operation Template
```cpp
template<typename ObjectType>
class DatabaseOperations {
public:
    [[nodiscard]] bool exists() const {
        if (auto db = database()) {
            return db->objectExists(static_cast<const ObjectType*>(this));
        }
        return false;
    }
    
    bool create() {
        if (auto db = database()) {
            return db->createObject(static_cast<const ObjectType*>(this));
        }
        return false;
    }
    
    bool drop() {
        if (auto db = database()) {
            return db->dropObject(static_cast<const ObjectType*>(this));
        }
        return false;
    }
};
```

## 5. Implementation Priority

1. **SqlIndex**: Implement enhanced column management and factory methods
2. **SqlRow**: Add type-safe value access and validation
3. **SqlView**: Implement dependency analysis and materialized view support
4. **Integration**: Enhance builder pattern integration
5. **Testing**: Comprehensive unit and integration tests

## 6. Testing Strategy

### 6.1 Unit Tests
- Test all new factory methods
- Validate type safety constraints
- Test fluent interface chains
- Verify metadata management

### 6.2 Integration Tests
- Test builder pattern integration
- Validate database operations
- Test cross-object relationships
- Performance benchmarking

### 6.3 Compatibility Tests
- Ensure backward compatibility
- Test migration scenarios
- Validate API consistency
- Cross-platform testing
