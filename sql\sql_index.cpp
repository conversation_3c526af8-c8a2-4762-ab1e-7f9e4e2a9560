#include "sql_index.h"

#include <sstream>
#include <algorithm>

#include "sql_query.h"
#include "sql_table.h"
#include "driver/sql_driver.h"

namespace database {

/**
 * @brief Private implementation class for SqlIndex
 *
 * This class provides the database interaction layer (Tier 3) for SqlIndex.
 * It manages the actual database operations and maintains connection state.
 */
class SqlIndexPrivate
{
public:
    SqlIndexPrivate() = default;
    ~SqlIndexPrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Associated table
    SqlTable table;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlIndex::SqlIndex()
    : SqlObject("", SqlObjectType::Index) {
}

SqlIndex::SqlIndex(std::string_view name, SqlIndexType indexType)
    : SqlObject(name, SqlObjectType::Index) {
    // Initialize metadata with basic information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->indexType = indexType;
}

SqlIndex::SqlIndex(std::string_view name, const SqlTable& table)
    : SqlObject(name, SqlObjectType::Index) {
    // Initialize metadata with table information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlIndexPrivate>();
        }
        d_ptr->setDatabase(db);
        d_ptr->table = table;
    }
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlIndex SqlIndex::fromDatabase(std::shared_ptr<SqlTable> table,
                                const std::string& indexName,
                                bool loadMetadata) {
    SqlIndex index(indexName);

    if (!table) {
        return index;
    }

    // Initialize metadata with table information
    if (!index.m_metadata) {
        index.m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    index.m_metadata->tableName = table->name();

    // Set up database connection
    auto db = table->database();
    if (db) {
        if (!index.d_ptr) {
            index.d_ptr = std::make_shared<SqlIndexPrivate>();
        }
        index.d_ptr->setDatabase(db);
        index.d_ptr->table = *table;

        if (loadMetadata) {
            index.loadMetadata();
        }
    }

    return index;
}

//----------------------------------------------------------------------
// Table Metadata
//----------------------------------------------------------------------

const SqlIndex::SqlIndexMetadata& SqlIndex::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    return *m_metadata;
}

SqlTable& SqlIndex::setMetadata(const SqlIndexMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    *m_metadata = metadata;

    // Return the associated table (this might need adjustment based on actual usage)
    if (d_ptr) {
        return d_ptr->table;
    }

    // Return a default table if no database connection
    static SqlTable defaultTable;
    return defaultTable;
}

bool SqlIndex::loadMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build query to get index information
        std::string sql = "SELECT * FROM INFORMATION_SCHEMA.STATISTICS WHERE INDEX_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, m_metadata ? m_metadata->tableName : "");

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to load index metadata: {}", query.lastError().message()));
            return false;
        }

        if (query.next()) {
            // Initialize metadata if not exists
            if (!m_metadata) {
                m_metadata = std::make_shared<SqlIndexMetadata>();
            }

            // Extract metadata from query result
            m_metadata->tableName = query.value("TABLE_NAME").to<std::string>();

            // Parse index type from database-specific information
            auto indexType = query.value("INDEX_TYPE").to<std::string>();
            if (indexType == "UNIQUE") {
                m_metadata->indexType = SqlIndexType::Unique;
                m_metadata->isUnique = true;
            } else if (indexType == "PRIMARY") {
                m_metadata->indexType = SqlIndexType::Primary;
                m_metadata->isUnique = true;
            } else {
                m_metadata->indexType = SqlIndexType::Normal;
            }

            // Get column information
            std::string columnName = query.value("COLUMN_NAME").to<std::string>();
            if (!columnName.empty()) {
                m_metadata->columns.push_back(columnName);

                // Parse sort order
                auto collation = query.value("COLLATION").to<std::string>();
                if (collation == "D") {
                    m_metadata->sortOrders.push_back(SqlSortOrder::Descending);
                } else {
                    m_metadata->sortOrders.push_back(SqlSortOrder::Ascending);
                }
            }

            d_ptr->clearError();
            return true;
        }

        d_ptr->setError("Index not found in database");
        return false;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception loading metadata: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

//----------------------------------------------------------------------
// Metadata Accessor Methods
//----------------------------------------------------------------------

SqlIndexType SqlIndex::indexType() const {
    return metadata().indexType;
}

void SqlIndex::setIndexType(SqlIndexType indexType) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->indexType = indexType;

    // Update unique flag based on index type
    if (indexType == SqlIndexType::Primary || indexType == SqlIndexType::Unique) {
        m_metadata->isUnique = true;
    }
}

std::string SqlIndex::tableName() const {
    return metadata().tableName;
}

void SqlIndex::setTableName(std::string tableName) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->tableName = std::move(tableName);
}

std::string SqlIndex::comment() const {
    return metadata().comment;
}

void SqlIndex::setComment(std::string comment) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->comment = std::move(comment);
}

std::vector<std::string> SqlIndex::columns() const {
    return metadata().columns;
}

void SqlIndex::setColumns(std::vector<std::string> columns) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->columns = std::move(columns);

    // Ensure sort orders vector matches columns size
    m_metadata->sortOrders.resize(m_metadata->columns.size(), SqlSortOrder::Ascending);
}

void SqlIndex::addColumn(std::string columnName, SqlSortOrder sortOrder) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }

    // Check if column already exists
    auto it = std::find(m_metadata->columns.begin(), m_metadata->columns.end(), columnName);
    if (it != m_metadata->columns.end()) {
        // Update existing column's sort order
        size_t index = std::distance(m_metadata->columns.begin(), it);
        if (index < m_metadata->sortOrders.size()) {
            m_metadata->sortOrders[index] = sortOrder;
        }
    } else {
        // Add new column
        m_metadata->columns.push_back(std::move(columnName));
        m_metadata->sortOrders.push_back(sortOrder);
    }
}

void SqlIndex::removeColumn(const std::string& columnName) {
    if (!m_metadata) {
        return;
    }

    auto it = std::find(m_metadata->columns.begin(), m_metadata->columns.end(), columnName);
    if (it != m_metadata->columns.end()) {
        size_t index = std::distance(m_metadata->columns.begin(), it);

        // Remove from columns vector
        m_metadata->columns.erase(it);

        // Remove corresponding sort order
        if (index < m_metadata->sortOrders.size()) {
            m_metadata->sortOrders.erase(m_metadata->sortOrders.begin() + index);
        }
    }
}

void SqlIndex::clearColumns() {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->columns.clear();
    m_metadata->sortOrders.clear();
}

std::vector<SqlSortOrder> SqlIndex::sortOrders() const {
    return metadata().sortOrders;
}

void SqlIndex::setSortOrders(std::vector<SqlSortOrder> sortOrders) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->sortOrders = std::move(sortOrders);
}

std::optional<std::string> SqlIndex::whereClause() const {
    return metadata().whereClause;
}

void SqlIndex::setWhereClause(std::optional<std::string> whereClause) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->whereClause = std::move(whereClause);
}

bool SqlIndex::isUnique() const {
    return metadata().isUnique;
}

void SqlIndex::setUnique(bool unique) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->isUnique = unique;

    // Update index type if setting unique
    if (unique && m_metadata->indexType == SqlIndexType::Normal) {
        m_metadata->indexType = SqlIndexType::Unique;
    } else if (!unique && m_metadata->indexType == SqlIndexType::Unique) {
        m_metadata->indexType = SqlIndexType::Normal;
    }
}

bool SqlIndex::isClustered() const {
    return metadata().isClustered;
}

void SqlIndex::setClustered(bool clustered) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->isClustered = clustered;

    // Update index type if setting clustered
    if (clustered) {
        m_metadata->indexType = SqlIndexType::Clustered;
    } else if (m_metadata->indexType == SqlIndexType::Clustered) {
        m_metadata->indexType = SqlIndexType::NonClustered;
    }
}

bool SqlIndex::isVisible() const {
    return metadata().isVisible;
}

void SqlIndex::setVisible(bool visible) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->isVisible = visible;
}

bool SqlIndex::isSystem() const {
    return metadata().isSystem;
}

void SqlIndex::setSystem(bool system) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->isSystem = system;
}

bool SqlIndex::isPrimaryKey() const {
    return metadata().indexType == SqlIndexType::Primary;
}

std::optional<size_t> SqlIndex::fillFactor() const {
    return metadata().fillfactor;
}

void SqlIndex::setFillFactor(std::optional<size_t> fillFactor) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->fillfactor = fillFactor;
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

SqlTable SqlIndex::table() const noexcept {
    if (d_ptr) {
        return d_ptr->table;
    }

    // Return a table constructed from metadata
    if (m_metadata && !m_metadata->tableName.empty()) {
        return SqlTable(m_metadata->tableName);
    }

    return SqlTable{};
}

void SqlIndex::setTable(const SqlTable& table) {
    // Initialize d_ptr if needed
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlIndexPrivate>();
    }

    d_ptr->table = table;

    // Update metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlIndexMetadata>();
    }
    m_metadata->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        d_ptr->setDatabase(db);
    }
}

std::shared_ptr<SqlDatabase> SqlIndex::database() const {
    if (d_ptr && d_ptr->database) {
        return d_ptr->database;
    }

    // Try to get database from associated table
    auto tbl = table();
    return tbl.database();
}

bool SqlIndex::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if index exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE INDEX_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata().tableName);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlIndex::create(bool ifNotExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE INDEX statement
        std::string sql = createSql(ifNotExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP INDEX statement
        std::string sql = dropSql(ifExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::rebuild() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build REBUILD INDEX statement (database-specific)
        std::string sql = "ALTER INDEX " + qualifiedName() + " REBUILD";

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to rebuild index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception rebuilding index: " + std::string(e.what()));
        return false;
    }
}

bool SqlIndex::analyze() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build ANALYZE INDEX statement
        std::string sql = "ANALYZE TABLE " + metadata().tableName;

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to analyze index: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception analyzing index: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Statistics and Usage Methods
//----------------------------------------------------------------------

size_t SqlIndex::sizeBytes() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index size (database-specific)
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'size'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata().tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlIndex::pageCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata().pages.has_value()) {
            return metadata().pages.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index page count
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'n_leaf_pages'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata().tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlIndex::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata().rows.has_value()) {
            return metadata().rows.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query for index row count
        std::string sql = "SELECT SUM(stat_value) FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ? AND stat_name = 'n_diff_pfx01'";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata().tableName);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

std::optional<double> SqlIndex::selectivity() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        return metadata().selectivity;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Calculate selectivity as distinct values / total rows
        std::string sql = "SELECT (SELECT COUNT(DISTINCT " + metadata().columns[0] + ") FROM " + metadata().tableName + ") / "
                                                                                                                        "(SELECT COUNT(*) FROM "
                          + metadata().tableName + ") AS selectivity";

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return std::nullopt;
        }

        return query.value(0).to<double>();

    } catch (const std::exception&) {
        return std::nullopt;
    }
}

bool SqlIndex::isUsed() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Check if index is used in query plans (database-specific)
        std::string sql = "SELECT COUNT(*) FROM performance_schema.events_statements_summary_by_digest "
                          "WHERE DIGEST_TEXT LIKE '%"
                          + metadata().tableName + "%' AND DIGEST_TEXT LIKE '%" + std::string(name()) + "%'";

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

std::unordered_map<std::string, std::string> SqlIndex::usageStatistics() const {
    std::unordered_map<std::string, std::string> stats;

    if (!d_ptr || !d_ptr->isValid()) {
        return stats;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Get various index statistics
        std::string sql = "SELECT stat_name, stat_value FROM mysql.innodb_index_stats WHERE index_name = ? AND table_name = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata().tableName);

        if (!query.execute()) {
            return stats;
        }

        while (query.next()) {
            std::string statName = query.value("stat_name").to<std::string>();
            std::string statValue = query.value("stat_value").to<std::string>();
            stats[statName] = statValue;
        }

    } catch (const std::exception&) {
        // Return empty map on error
    }

    return stats;
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlIndex::qualifiedName() const {
    // For indexes, qualified name is typically just the index name
    // Some databases might require table.index_name format
    return std::string(name());
}
std::string SqlIndex::createSql(bool ifNotExists) const {
    std::ostringstream sql;

    // Handle different index types
    switch (metadata().indexType) {
    case SqlIndexType::Primary:
        sql << "ALTER TABLE " << metadata().tableName << " ADD PRIMARY KEY (";
        break;
    case SqlIndexType::Unique:
        sql << "CREATE UNIQUE INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata().tableName << " (";
        break;
    case SqlIndexType::FullText:
        sql << "CREATE FULLTEXT INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata().tableName << " (";
        break;
    default:
        sql << "CREATE INDEX ";
        if (ifNotExists) {
            sql << "IF NOT EXISTS ";
        }
        sql << name() << " ON " << metadata().tableName << " (";
        break;
    }

    // Add columns with sort orders
    const auto& columns = metadata().columns;
    const auto& sortOrders = metadata().sortOrders;

    if (columns.empty()) {
        // If no columns specified, this is an error condition
        sql << "/* ERROR: No columns specified for index */";
    } else {
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << columns[i];

            // Add sort order if specified and not default
            if (i < sortOrders.size() && sortOrders[i] == SqlSortOrder::Descending) {
                sql << " DESC";
            }
        }
    }

    sql << ")";

    // Add WHERE clause for partial indexes
    if (metadata().whereClause.has_value() && !metadata().whereClause->empty()) {
        sql << " WHERE " << metadata().whereClause.value();
    }

    // Add fill factor if specified
    if (metadata().fillfactor.has_value()) {
        sql << " WITH FILLFACTOR = " << metadata().fillfactor.value();
    }

    // Add comment if specified
    if (!metadata().comment.empty()) {
        sql << " COMMENT '" << metadata().comment << "'";
    }

    return sql.str();
}

std::string SqlIndex::dropSql(bool ifExists) const {
    std::ostringstream sql;

    if (metadata().indexType == SqlIndexType::Primary) {
        sql << "ALTER TABLE " << metadata().tableName << " DROP PRIMARY KEY";
    } else {
        sql << "DROP INDEX ";
        if (ifExists) {
            sql << "IF EXISTS ";
        }
        sql << name() << " ON " << metadata().tableName;
    }

    return sql.str();
}

} // namespace database 
