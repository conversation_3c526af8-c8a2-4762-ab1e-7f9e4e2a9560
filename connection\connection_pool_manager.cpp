#include "connection_pool_manager.h"
#include "../logging/database_logger.h"
#include <iostream>
#include <algorithm>

namespace database {

ConnectionPoolManager& ConnectionPoolManager::instance() {
    static ConnectionPoolManager instance;
    return instance;
}

std::shared_ptr<ConnectionPool> ConnectionPoolManager::createPool(
    const std::string& poolName,
    const std::string& driverName,
    const ConnectionParams& params,
    size_t initialSize,
    size_t maxSize,
    std::chrono::milliseconds timeout) {
    
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // Check if the pool already exists
    auto it = m_pools.find(poolName);
    if (it != m_pools.end()) {
        std::cerr << "Pool '" << poolName << "' already exists" << std::endl;
        
        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Pool '" + poolName + "' already exists",
            "ConnectionPoolManager");
        
        return it->second;
    }
    
    // Create the pool
    auto pool = std::make_shared<ConnectionPool>(
        driverName, params, initialSize, maxSize, timeout);
    
    // Store the pool
    m_pools[poolName] = pool;
    
    std::cout << "Created connection pool '" << poolName << "' for driver '" << driverName << "'" << std::endl;
    
    // Log creation
    DatabaseLogger::instance().log(
        LogLevel::Info,
        "Created connection pool '" + poolName + "' for driver '" + driverName + "'",
        "ConnectionPoolManager");
    
    return pool;
}

std::shared_ptr<ConnectionPool> ConnectionPoolManager::getPool(const std::string& poolName) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_pools.find(poolName);
    if (it != m_pools.end()) {
        return it->second;
    }
    
    std::cerr << "Pool '" << poolName << "' not found" << std::endl;
    
    // Log error
    DatabaseLogger::instance().log(
        LogLevel::Error,
        "Pool '" + poolName + "' not found",
        "ConnectionPoolManager");
    
    return nullptr;
}

bool ConnectionPoolManager::removePool(const std::string& poolName) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_pools.find(poolName);
    if (it == m_pools.end()) {
        std::cerr << "Pool '" << poolName << "' not found" << std::endl;
        
        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Pool '" + poolName + "' not found",
            "ConnectionPoolManager");
        
        return false;
    }
    
    // Close the pool
    it->second->close();
    
    // Remove the pool
    m_pools.erase(it);
    
    std::cout << "Removed connection pool '" << poolName << "'" << std::endl;
    
    // Log removal
    DatabaseLogger::instance().log(
        LogLevel::Info,
        "Removed connection pool '" + poolName + "'",
        "ConnectionPoolManager");
    
    return true;
}

std::shared_ptr<SqlDatabase> ConnectionPoolManager::getConnection(const std::string& poolName) {
    auto pool = getPool(poolName);
    if (!pool) {
        return nullptr;
    }
    
    return pool->getConnection();
}

void ConnectionPoolManager::releaseConnection(std::shared_ptr<SqlDatabase> connection, const std::string& poolName) {
    if (!connection) {
        return;
    }
    
    auto pool = getPool(poolName);
    if (!pool) {
        std::cerr << "Cannot release connection: pool '" << poolName << "' not found" << std::endl;
        
        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Cannot release connection: pool '" + poolName + "' not found",
            "ConnectionPoolManager");
        
        return;
    }
    
    pool->releaseConnection(connection);
}

void ConnectionPoolManager::closeAll() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    for (auto& pair : m_pools) {
        pair.second->close();
    }
    
    m_pools.clear();
    
    std::cout << "Closed all connection pools" << std::endl;
    
    // Log closure
    DatabaseLogger::instance().log(
        LogLevel::Info,
        "Closed all connection pools",
        "ConnectionPoolManager");
}

} // namespace database
