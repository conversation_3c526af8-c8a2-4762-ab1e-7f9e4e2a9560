﻿#ifndef DATABASE_TYPES_H
#define DATABASE_TYPES_H

#include <string>
#include <vector>
#include <unordered_map>

#include "variant.h"
#include "sql_enums.h"

namespace database {

using SqlOperator = SqlConditionOperator;

// Common type definitions
using Data = Variant;
using DataList = std::vector<Data>;
using DataLists = std::vector<DataList>;
using DataMap = std::unordered_map<std::string, Data>;
using DataMapList = std::vector<DataMap>;

/**
 * @brief Transparent hash for string types
 *
 * This hash allows heterogeneous lookup in unordered_map with string_view
 * without converting to std::string. It's compatible with C++20's transparent
 * comparator concept.
 */
struct StringViewHash {
    using is_transparent = void; // Mark as transparent

    // Hash functions for different string types
    size_t operator()(const char* str) const {
        return std::hash<std::string_view>{}(str);
    }

    size_t operator()(std::string_view str) const {
        return std::hash<std::string_view>{}(str);
    }

    size_t operator()(const std::string& str) const {
        return std::hash<std::string_view>{}(str);
    }
};

} // namespace database

#endif // DATABASE_TYPES_H
