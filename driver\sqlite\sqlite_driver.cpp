﻿#include "sqlite_driver.h"

#include <iostream>
#include <unordered_set>
#include <format>
#include <charconv>
#include <sqlite3.h>

#include "sqlite_statement.h"
#include "sqlite_error.h"

namespace database {

// Private implementation class for SqliteDriver
class SqliteDriverPrivate {
public:
    SqliteDriverPrivate() {
        // Reserve space for active statements to prevent frequent reallocations
        statements.reserve(16);
    }
    ~SqliteDriverPrivate() = default;

    // Supported features
    const std::vector<DriverFeature> supportedFeatures = {
        DriverFeature::Transactions,
        DriverFeature::PreparedStatements,
        DriverFeature::NamedParameters,
        DriverFeature::SavePoints
    };

    // SQLite connection handle
    sqlite3* handle { nullptr };

    mutable SqlError lastError {};

    // Active statements
    std::unordered_set<SqliteStatement*> statements {};
    std::mutex statementsMutex {};
};

SqliteDriver::SqliteDriver()
    : d_ptr(std::make_unique<SqliteDriverPrivate>()) {
    sqlite3_initialize();
}

SqliteDriver::~SqliteDriver() noexcept
{
    try {
        // Call disconnect to clean up resources
        disconnect();

        // Shutdown SQLite library
        sqlite3_shutdown();
    }
    catch (const std::exception& e) {
        // Log error but don't throw from destructor
        std::cerr << "Error during SqliteDriver cleanup: " << e.what() << std::endl;
    }
}

bool SqliteDriver::connect(const ConnectionParams& params) {
    // Check if already connected
    if (isConnected()) {
        return true;
    }

    // Get database path
    auto dbPath = params.database();
    if (dbPath.empty()) {
        // Use in-memory database if no path is specified
        dbPath = params.option("path", ":memory:");
    }

    // Parse connection options with default values
    struct {
        int timeOut { 5000 }; // Default: 5 seconds
        bool sharedCache { false };
        bool openReadOnly { false };
        bool openUri { false };
        bool noFollow { false };
    } options;

    // Process connection options
    for (const auto& [key, value] : params.options()) {
        if (key == "timeout") {
            try {
                options.timeOut = std::stoi(value);
            } catch (const std::exception& e) {
                std::cerr << "Invalid timeout value: " << value << std::endl;
            }
        } else if (key == "shared_cache") {
            options.sharedCache = (value == "true" || value == "1");
        } else if (key == "readonly") {
            options.openReadOnly = (value == "true" || value == "1");
        } else if (key == "uri") {
            options.openUri = (value == "true" || value == "1");
        } else if (key == "nofollow") {
            options.noFollow = (value == "true" || value == "1");
        }
    }

    // Set up open mode
    int openMode = (options.openReadOnly ? SQLITE_OPEN_READONLY : (SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE));
    openMode |= (options.sharedCache ? SQLITE_OPEN_SHAREDCACHE : SQLITE_OPEN_PRIVATECACHE);
    if (options.openUri) {
        openMode |= SQLITE_OPEN_URI;
    }
#if defined(SQLITE_OPEN_NOFOLLOW)
    if (options.noFollow) {
        openMode |= SQLITE_OPEN_NOFOLLOW;
    }
#endif
    openMode |= SQLITE_OPEN_NOMUTEX;

    // Close any existing connection
    if (d_ptr->handle) {
        sqlite3_close_v2(d_ptr->handle);
        d_ptr->handle = nullptr;
    }

    // Open the database
    int result = sqlite3_open_v2(dbPath.data(), &d_ptr->handle, openMode, nullptr);

    if (result != SQLITE_OK) {
        std::string errorMsg = "Failed to open database: ";
        if (d_ptr->handle) {
            errorMsg += sqlite3_errmsg(d_ptr->handle);
            sqlite3_close(d_ptr->handle);
            d_ptr->handle = nullptr;
        } else {
            errorMsg += "Unknown error";
        }
        d_ptr->lastError = makeError(nullptr, errorMsg, ErrorCode::ConnectionFailed);
        return false;
    }

    // Set timeout
    sqlite3_busy_timeout(d_ptr->handle, options.timeOut);

    // Enable extended result codes
    sqlite3_extended_result_codes(d_ptr->handle, 1);

    return true;
}

bool SqliteDriver::disconnect()
{
    if (!d_ptr->handle) {
        return true; // Already disconnected
    }

    // Make a copy to avoid iterator invalidation and deadlock during close()
    std::unordered_set<SqliteStatement*> statementsCopy;
    {
        std::lock_guard<std::mutex> lock(d_ptr->statementsMutex);
        statementsCopy = d_ptr->statements;
    }

    // Close all active statements first
    for (auto* statement : statementsCopy) {
        if (statement) {
            statement->close();
        }
    }

    {
        std::lock_guard<std::mutex> lock(d_ptr->statementsMutex);
        if (!d_ptr->statements.empty()) {
            std::cerr << "SqliteDriver::disconnect: statements is NOT empty during disconnect." << std::endl;
            d_ptr->statements.clear();
        }
    }

    // Close the connection
    int result = sqlite3_close_v2(d_ptr->handle); // Use sqlite3_close_v2 for safer cleanup
    if (result != SQLITE_OK) {
        d_ptr->lastError = makeError(d_ptr->handle, "Failed to close database", ErrorCode::ConnectionClosed);
        return false;
    }

    d_ptr->handle = nullptr;
    return true;
}

bool SqliteDriver::isConnected() const noexcept {
    return d_ptr->handle != nullptr;
}

SqlDriver::ValidationResult SqliteDriver::validateConnection(std::string_view query) {
    if (!isConnected()) {
        return ValidationResult::Invalid;
    }

    // Use the provided query or a default one
    auto validationQuery = query.empty() ? getDefaultValidationQuery() : query;

    // Try to execute the query
    auto stmt = createStatement();
    if (!stmt) {
        return ValidationResult::Invalid;
    }
    if (!stmt->prepare(validationQuery)) {
        return ValidationResult::Invalid;
    }

    if (!stmt->execute()) {
        return ValidationResult::Invalid;
    }

    return ValidationResult::Valid;
}

bool SqliteDriver::ping() {
    return validateConnection() == ValidationResult::Valid;
}

std::shared_ptr<SqlStatement> SqliteDriver::createStatement() {
    if (!isConnected()) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return nullptr;
    }

    return std::make_shared<SqliteStatement>(this);
}

bool SqliteDriver::beginTransaction(TransactionIsolation level) {
    // SQLite only supports "BEGIN" (deferred), "BEGIN IMMEDIATE", and "BEGIN EXCLUSIVE"
    std::string_view sql;
    switch (level) {
        case TransactionIsolation::ReadUncommitted:
            sql = "BEGIN";
            break;
        case TransactionIsolation::ReadCommitted:
            sql = "BEGIN IMMEDIATE";
            break;
        case TransactionIsolation::RepeatableRead:
        case TransactionIsolation::Serializable:
            sql = "BEGIN EXCLUSIVE";
            break;
        default:
            sql = "BEGIN";
            break;
    }

    return executeSimpleQuery(sql);
}

bool SqliteDriver::commitTransaction() {
    return executeSimpleQuery("COMMIT");
}

bool SqliteDriver::rollbackTransaction() {
    return executeSimpleQuery("ROLLBACK");
}

bool SqliteDriver::createSavepoint(std::string_view name) {
    return executeSimpleQuery(std::format("SAVEPOINT {}", name));
}

bool SqliteDriver::rollbackToSavepoint(std::string_view name) {
    return executeSimpleQuery(std::format("ROLLBACK TO SAVEPOINT {}", name));
}

bool SqliteDriver::releaseSavepoint(std::string_view name) {
    return executeSimpleQuery(std::format("RELEASE SAVEPOINT {}", name));
}

ConnectionMetadata SqliteDriver::metadata() const {
    ConnectionMetadata metadata;

    if (!d_ptr->handle) {
        return metadata;
    }

    const char* versionStr = sqlite3_libversion();
    metadata.setProductName("SQLite");
    metadata.setProductVersion(versionStr);

    // Parse version string
    std::string_view version(versionStr);
    size_t firstDot = version.find('.');
    size_t secondDot = version.find('.', firstDot + 1);

    if (firstDot != std::string_view::npos && secondDot != std::string_view::npos) {
        try {
            std::string_view majorStr = version.substr(0, firstDot);
            std::string_view minorStr = version.substr(firstDot + 1, secondDot - firstDot - 1);

            // Convert string_view to int using from_chars for better performance
            int majorVersion = 0;
            int minorVersion = 0;

            // Use std::from_chars for more efficient string to int conversion
            if (auto [ptr, ec] = std::from_chars(majorStr.data(), majorStr.data() + majorStr.size(), majorVersion);
                ec == std::errc()) {
                metadata.setMajorVersion(majorVersion);
            }

            if (auto [ptr, ec] = std::from_chars(minorStr.data(), minorStr.data() + minorStr.size(), minorVersion);
                ec == std::errc()) {
                metadata.setMinorVersion(minorVersion);
            }
        } catch (const std::exception&) {
            // Ignore conversion errors
        }
    }

    // Get the database filename
    const char* filename = sqlite3_db_filename(d_ptr->handle, "main");
    if (filename) {
        metadata.setURL(filename);
    }

    metadata.setSupportsTransactions(true);
    metadata.setSupportsSavepoints(true);
    metadata.setSupportsBatchUpdates(false);

    return metadata;
}

std::string_view SqliteDriver::name() const {
    return "SQLite";
}

std::string_view SqliteDriver::version() const {
    return sqlite3_libversion();
}

const SqlError& SqliteDriver::lastError() const noexcept {
    return d_ptr->lastError;
}

bool SqliteDriver::supportsFeature(DriverFeature feature) const noexcept {
    const auto& features = d_ptr->supportedFeatures;
    return std::find(features.begin(), features.end(), feature) != features.end();
}

std::vector<DriverFeature> SqliteDriver::getSupportedFeatures() const noexcept {
    return d_ptr->supportedFeatures;
}

int SqliteDriver::getMaxConnections() const noexcept {
    return 0; // No limit
}

TransactionIsolation SqliteDriver::getDefaultTransactionIsolation() const noexcept {
    return TransactionIsolation::Serializable; // SQLite uses serializable by default
}

std::string_view SqliteDriver::getDefaultValidationQuery() const noexcept {
    return "SELECT 1";
}

sqlite3* SqliteDriver::getHandle() const noexcept {
    return d_ptr->handle;
}

void SqliteDriver::addActiveStatement(SqliteStatement* stmt)
{
    if (stmt && d_ptr) {
        std::lock_guard<std::mutex> lock(d_ptr->statementsMutex);
        d_ptr->statements.insert(stmt);
    }
}

void SqliteDriver::removeActiveStatement(SqliteStatement* stmt)
{
    if (stmt && d_ptr) {
        std::lock_guard<std::mutex> lock(d_ptr->statementsMutex);
        d_ptr->statements.erase(stmt);
    }
}

SqlError SqliteDriver::makeError(sqlite3* handle, std::string_view message, ErrorCode code) {
    std::string sqliteMessage;
    int extendedCode = 0;

    if (handle) {
        sqliteMessage = sqlite3_errmsg(handle);
        extendedCode = sqlite3_extended_errcode(handle);

        // If we have a SQLite error code but were given a generic error code,
        // map the SQLite error code to our custom ErrorCode
        if (extendedCode != 0 && code == ErrorCode::Unknown) {
            code = mapSqliteErrorToErrorCode(extendedCode);
        }
    }

    // Build a more informative error message
    std::string fullMessage;

    if (sqliteMessage.empty()) {
        fullMessage = std::format("{}", message);
    } else {
        fullMessage = std::format("{}: {}", message, sqliteMessage);
    }

    // Add error code category and description
    if (code != ErrorCode::Unknown) {
        if (extendedCode != 0) {
            fullMessage = std::format("{} [{}: {}, SQLite code: {}]",
                                      fullMessage, getErrorCategory(code), errorCodeToString(code), extendedCode);
        } else {
            fullMessage = std::format("{} [{}: {}]",
                                      fullMessage, getErrorCategory(code), errorCodeToString(code));
        }
    }

    return SqlError(fullMessage, code);
}

bool SqliteDriver::executeSimpleQuery(std::string_view sql) {
    if (!isConnected()) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Create a null-terminated string from the string_view
    // Reserve space to avoid reallocations
    std::string sqlStr;
    sqlStr.reserve(sql.size() + 1);
    sqlStr.assign(sql);

    char* errorMsg = nullptr;
    int result = sqlite3_exec(d_ptr->handle, sqlStr.c_str(), nullptr, nullptr, &errorMsg);

    if (result != SQLITE_OK) {
        std::string_view error = errorMsg ? errorMsg : "Unknown error";
        ErrorCode errorCode = mapSqliteErrorToErrorCode(result);

        std::string message = std::format("Error executing SQL ({}/{}): {}",
                                          result, errorCodeToString(errorCode), error);

        d_ptr->lastError = SqlError(message, errorCode);

        if (errorMsg) {
            sqlite3_free(errorMsg);
        }

        return false;
    }

    return true;

    // Method B - Commented out as in original
    /*
    auto stmt = prepareStatement(sql);
    if (!stmt) {
        return false;
    }

    return stmt->execute();
    */
}

std::unique_ptr<SqlDriver> createSqliteDriver() {
    return std::make_unique<SqliteDriver>();
}

} // namespace database
