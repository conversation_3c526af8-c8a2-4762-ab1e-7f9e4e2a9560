#ifndef DATABASE_SQL_INDEX_H
#define DATABASE_SQL_INDEX_H

#include <string>
#include <string_view>
#include <vector>
#include <optional>
#include <chrono>
#include <unordered_map>
#include <algorithm>
#include <type_traits>
#include <memory>
#include <functional>

#include "sql_object.h"

namespace database {

// Forward declarations
class SqlIndexPrivate;
class SqlTable;
class SqlDatabase;

/**
 * @brief Type trait for index-like objects
 */
template<typename T>
struct is_index_like {
private:
    template<typename U>
    static auto test(int) -> decltype(
        std::declval<const U&>().indexType(),
        std::declval<const U&>().columns(),
        std::declval<const U&>().isUnique(),
        std::true_type{}
    );

    template<typename>
    static std::false_type test(...);

public:
    static constexpr bool value = decltype(test<T>(0))::value;
};

template<typename T>
constexpr bool is_index_like_v = is_index_like<T>::value;

/**
 * @brief Represents a database index with lazy-loaded metadata
 *
 * This class serves as the core representation of a database index,
 * providing both SQL building capabilities and metadata access through
 * lazy loading for optimal performance.
 */
class SqlIndex final : public SqlObject {
public:
    /**
     * @brief Enhanced index metadata structure with modern C++ optimizations
     */
    struct SqlIndexMetadata {
        std::string tableName;                              ///< Parent table name
        std::string comment;                                ///< Index comment/description
        SqlIndexType indexType = SqlIndexType::Unknown;    ///< Index type
        std::vector<std::string> columns;                   ///< Indexed columns
        std::vector<SqlSortOrder> sortOrders;               ///< Sort order for each column
        std::optional<std::string> whereClause;             ///< Partial index condition
        std::optional<size_t> fillfactor;                   ///< Fill factor percentage
        bool isUnique = false;                              ///< Is unique index
        bool isClustered = false;                           ///< Is clustered index
        bool isVisible = true;                              ///< Is visible to query optimizer
        bool isSystem = false;                              ///< Is system-generated index
        std::optional<std::chrono::system_clock::time_point> createdTime; ///< Creation time
        std::optional<size_t> pages;                        ///< Number of pages
        std::optional<size_t> rows;                         ///< Number of rows
        std::optional<double> selectivity;                  ///< Index selectivity
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

        // Enhanced constructors with perfect forwarding
        SqlIndexMetadata() = default;

        template<typename TableName, typename Columns>
        SqlIndexMetadata(TableName&& tableName, SqlIndexType indexType, Columns&& columns)
            : tableName(std::forward<TableName>(tableName))
            , indexType(indexType)
            , columns(std::forward<Columns>(columns)) {
            // Initialize sort orders to ascending by default
            sortOrders.resize(this->columns.size(), SqlSortOrder::Ascending);
        }

        // Enhanced column management with modern C++ features
        template<typename ColumnRange>
        void setColumns(ColumnRange&& columnRange) {
            columns.clear();
            sortOrders.clear();

            if constexpr (std::is_same_v<std::decay_t<ColumnRange>, std::vector<std::string>>) {
                columns.reserve(columnRange.size());
                sortOrders.reserve(columnRange.size());
            }

            for (auto&& column : columnRange) {
                columns.emplace_back(std::forward<decltype(column)>(column));
                sortOrders.emplace_back(SqlSortOrder::Ascending); // Default
            }
        }

        // Utility methods for index type checking
        [[nodiscard]] bool isUniqueIndex() const noexcept {
            return isUnique || indexType == SqlIndexType::Unique || indexType == SqlIndexType::Primary;
        }

        [[nodiscard]] bool isClusteredIndex() const noexcept {
            return isClustered || indexType == SqlIndexType::Clustered;
        }

        [[nodiscard]] bool isPrimaryKeyIndex() const noexcept {
            return indexType == SqlIndexType::Primary;
        }

        [[nodiscard]] bool isPartialIndex() const noexcept {
            return whereClause.has_value() && !whereClause->empty();
        }

        // Column management utilities
        [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept {
            return std::find(columns.begin(), columns.end(), columnName) != columns.end();
        }

        [[nodiscard]] std::optional<size_t> getColumnIndex(std::string_view columnName) const noexcept {
            auto it = std::find(columns.begin(), columns.end(), columnName);
            return (it != columns.end()) ? std::optional<size_t>(std::distance(columns.begin(), it)) : std::nullopt;
        }

        [[nodiscard]] SqlSortOrder getColumnSortOrder(std::string_view columnName) const noexcept {
            if (auto index = getColumnIndex(columnName); index && *index < sortOrders.size()) {
                return sortOrders[*index];
            }
            return SqlSortOrder::Ascending; // Default
        }

        void setColumnSortOrder(std::string_view columnName, SqlSortOrder order) {
            if (auto index = getColumnIndex(columnName); index && *index < sortOrders.size()) {
                sortOrders[*index] = order;
            }
        }
    };

    // Enhanced constructors with modern C++ features
    SqlIndex() noexcept;
    explicit SqlIndex(std::string_view name, SqlIndexType indexType = SqlIndexType::Unknown) noexcept;
    explicit SqlIndex(std::string_view name, const SqlTable& table) noexcept;

    // Template constructor for type-safe index creation with columns
    template<typename ColumnRange>
    SqlIndex(std::string_view name, const SqlTable& table, SqlIndexType indexType, ColumnRange&& columns)
        : SqlIndex(name, table) {
        if (auto meta = metadata()) {
            meta->indexType = indexType;
            meta->setColumns(std::forward<ColumnRange>(columns));
        }
    }

    // Enhanced copy/move operations with optimizations
    SqlIndex(const SqlIndex& other) = default;
    SqlIndex(SqlIndex&& other) noexcept = default;
    SqlIndex& operator=(const SqlIndex& other) = default;
    SqlIndex& operator=(SqlIndex&& other) noexcept = default;

    ~SqlIndex() = default;

    //----------------------------------------------------------------------
    // Enhanced Static Factory Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlIndex fromDatabase(std::shared_ptr<SqlTable> table,
                                               const std::string& indexName,
                                               bool loadMetadata = true);

    // Modern factory methods with perfect forwarding
    template<typename... Args>
    [[nodiscard]] static SqlIndex create(std::string_view name, SqlIndexType indexType, Args&&... args) {
        SqlIndex index(name, indexType);
        if constexpr (sizeof...(args) > 0) {
            auto meta = index.metadata();
            if (meta) {
                // Apply additional metadata using fold expressions
                (index.applyMetadataArg(std::forward<Args>(args)), ...);
            }
        }
        return index;
    }

    // Utility factory methods for common index types
    [[nodiscard]] static SqlIndex primaryKey(std::string_view tableName,
                                            const std::vector<std::string>& columns) {
        auto index = SqlIndex("PRIMARY", SqlIndexType::Primary);
        if (index.metadata()) {
            index.metadata()->tableName = tableName;
            index.metadata()->setColumns(columns);
            index.metadata()->isUnique = true;
        }
        return index;
    }

    [[nodiscard]] static SqlIndex unique(std::string_view name, std::string_view tableName,
                                        const std::vector<std::string>& columns) {
        auto index = SqlIndex(name, SqlIndexType::Unique);
        if (index.metadata()) {
            index.metadata()->tableName = tableName;
            index.metadata()->setColumns(columns);
            index.metadata()->isUnique = true;
        }
        return index;
    }

    [[nodiscard]] static SqlIndex clustered(std::string_view name, std::string_view tableName,
                                           const std::vector<std::string>& columns) {
        auto index = SqlIndex(name, SqlIndexType::Clustered);
        if (index.metadata()) {
            index.metadata()->tableName = tableName;
            index.metadata()->setColumns(columns);
            index.metadata()->isClustered = true;
        }
        return index;
    }

    [[nodiscard]] static SqlIndex normal(std::string_view name, std::string_view tableName,
                                        const std::vector<std::string>& columns) {
        auto index = SqlIndex(name, SqlIndexType::Normal);
        if (index.metadata()) {
            index.metadata()->tableName = tableName;
            index.metadata()->setColumns(columns);
        }
        return index;
    }

    //----------------------------------------------------------------------
    // Enhanced Metadata Management with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] SqlIndexMetadata* metadata() const noexcept;
    SqlIndex& setMetadata(const SqlIndexMetadata& metadata);
    bool loadMetadata() override;
    bool refreshMetadata() override;
    [[nodiscard]] bool hasMetadata() const noexcept override;

    // Core properties with optimized accessors and fluent interface
    [[nodiscard]] SqlIndexType indexType() const noexcept;
    SqlIndex& setIndexType(SqlIndexType indexType) noexcept;

    [[nodiscard]] std::string_view tableName() const noexcept;
    SqlIndex& setTableName(std::string tableName);

    [[nodiscard]] std::string_view comment() const noexcept;
    SqlIndex& setComment(std::string comment);

    // Enhanced column management with modern C++ features
    [[nodiscard]] std::vector<std::string> columns() const noexcept;

    template<typename ColumnRange>
    SqlIndex& setColumns(ColumnRange&& columnRange) {
        if (auto meta = metadata()) {
            meta->setColumns(std::forward<ColumnRange>(columnRange));
        }
        return *this;
    }

    SqlIndex& addColumn(std::string columnName, SqlSortOrder sortOrder = SqlSortOrder::Ascending);
    SqlIndex& removeColumn(const std::string& columnName);
    SqlIndex& clearColumns();

    // Template-based column operations
    template<typename Predicate>
    [[nodiscard]] std::vector<std::string> filterColumns(Predicate&& pred) const {
        auto cols = columns();
        std::vector<std::string> result;
        std::copy_if(cols.begin(), cols.end(), std::back_inserter(result),
                    std::forward<Predicate>(pred));
        return result;
    }

    [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept;
    [[nodiscard]] size_t columnCount() const noexcept;

    // Enhanced sort order management
    [[nodiscard]] std::vector<SqlSortOrder> sortOrders() const noexcept;
    SqlIndex& setSortOrders(std::vector<SqlSortOrder> sortOrders);
    SqlIndex& setSortOrder(std::string_view columnName, SqlSortOrder order);
    SqlIndex& setSortOrder(size_t columnIndex, SqlSortOrder order);
    [[nodiscard]] SqlSortOrder getSortOrder(std::string_view columnName) const noexcept;
    [[nodiscard]] SqlSortOrder getSortOrder(size_t columnIndex) const noexcept;

    // Fluent interface for sort orders
    SqlIndex& ascending(std::string_view columnName) { return setSortOrder(columnName, SqlSortOrder::Ascending); }
    SqlIndex& descending(std::string_view columnName) { return setSortOrder(columnName, SqlSortOrder::Descending); }
    SqlIndex& ascending(size_t columnIndex) { return setSortOrder(columnIndex, SqlSortOrder::Ascending); }
    SqlIndex& descending(size_t columnIndex) { return setSortOrder(columnIndex, SqlSortOrder::Descending); }

    // Enhanced property management with fluent interface
    [[nodiscard]] std::optional<std::string> whereClause() const noexcept;
    SqlIndex& setWhereClause(std::optional<std::string> whereClause);
    SqlIndex& setWhereClause(std::string_view whereClause) { return setWhereClause(std::string(whereClause)); }

    [[nodiscard]] bool isUnique() const noexcept;
    SqlIndex& setUnique(bool unique = true) noexcept;
    SqlIndex& unique() noexcept { return setUnique(true); }

    [[nodiscard]] bool isClustered() const noexcept;
    SqlIndex& setClustered(bool clustered = true) noexcept;
    SqlIndex& clustered() noexcept { return setClustered(true); }

    [[nodiscard]] bool isVisible() const noexcept;
    SqlIndex& setVisible(bool visible = true) noexcept;
    SqlIndex& visible() noexcept { return setVisible(true); }
    SqlIndex& invisible() noexcept { return setVisible(false); }

    [[nodiscard]] bool isSystem() const noexcept;
    SqlIndex& setSystem(bool system = true) noexcept;

    [[nodiscard]] bool isPrimaryKey() const noexcept;

    [[nodiscard]] std::optional<size_t> fillFactor() const noexcept;
    SqlIndex& setFillFactor(std::optional<size_t> fillFactor) noexcept;
    SqlIndex& setFillFactor(size_t fillFactor) noexcept { return setFillFactor(std::optional<size_t>(fillFactor)); }

    //----------------------------------------------------------------------
    // Enhanced Database Interaction Operations
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    SqlIndex& setTable(const SqlTable& table);

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    void setDatabase(std::shared_ptr<SqlDatabase> db) override;

    // Database operations with enhanced error handling
    [[nodiscard]] bool exists() const;
    bool create(bool ifNotExists = true);
    bool drop(bool ifExists = true);
    bool rebuild();
    bool analyze();
    bool reindex();

    // Enhanced statistics and performance metrics
    [[nodiscard]] size_t sizeBytes() const;
    [[nodiscard]] size_t pageCount() const;
    [[nodiscard]] size_t rowCount() const;
    [[nodiscard]] std::optional<double> selectivity() const;
    [[nodiscard]] bool isUsed() const;
    [[nodiscard]] std::unordered_map<std::string, std::string> usageStatistics() const;

    // Performance analysis
    [[nodiscard]] double estimatedCost() const;
    [[nodiscard]] size_t estimatedRows() const;
    [[nodiscard]] bool isEfficient() const;
    [[nodiscard]] std::vector<std::string> getOptimizationSuggestions() const;

    //----------------------------------------------------------------------
    // Enhanced SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    // Enhanced SQL generation methods
    [[nodiscard]] std::string createSql(bool ifNotExists = false) const;
    [[nodiscard]] std::string dropSql(bool ifExists = false) const;
    [[nodiscard]] std::string rebuildSql() const;
    [[nodiscard]] std::string analyzeSql() const;

private:
    // Helper methods for metadata management
    template<typename T>
    void applyMetadataArg(T&& arg) {
        if constexpr (std::is_same_v<std::decay_t<T>, std::vector<std::string>>) {
            setColumns(std::forward<T>(arg));
        } else if constexpr (std::is_same_v<std::decay_t<T>, bool>) {
            setUnique(std::forward<T>(arg));
        } else if constexpr (std::is_same_v<std::decay_t<T>, std::string>) {
            setWhereClause(std::forward<T>(arg));
        }
        // Add more metadata argument types as needed
    }

    // Enhanced member variables with better encapsulation
    mutable std::shared_ptr<SqlIndexMetadata> m_metadata;
    std::shared_ptr<SqlIndexPrivate> d_ptr;
};

} // namespace database

// Enhanced hash specialization for SqlIndex with modern C++ features
namespace std {
template<>
struct hash<database::SqlIndex> {
    [[nodiscard]] size_t operator()(const database::SqlIndex& index) const noexcept {
        // Use the base SqlObject hash combined with index-specific information
        size_t h1 = index.hash();

        // Hash the table name if available
        size_t h2 = 0;
        try {
            auto tableName = index.tableName();
            if (!tableName.empty()) {
                h2 = std::hash<std::string_view>{}(tableName);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the index type and column count for additional uniqueness
        size_t h3 = static_cast<size_t>(index.indexType());
        size_t h4 = index.columnCount();

        return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3);
    }
};
} // namespace std

#endif // DATABASE_SQL_INDEX_H
