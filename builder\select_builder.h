#ifndef DATABASE_SELECT_BUILDER_H
#define DATABASE_SELECT_BUILDER_H

#include <vector>
#include <string>
#include <string_view>
#include <optional>

#include "sql_builder.h"

namespace database {

/**
 * @brief Builder for SQL SELECT queries
 *
 * This class provides a fluent interface for building SQL SELECT queries.
 * It supports selecting columns, joining tables, adding WHERE conditions,
 * and other SELECT-specific clauses.
 */
class SelectBuilder : public SqlBuilder {
public:
    /**
     * @brief Constructor
     */
    SelectBuilder();

    /**
     * @brief Select specific columns using variadic templates
     * @tparam Args Types of the columns (must be convertible to SqlColumn)
     * @param columns The columns to select
     * @return Reference to this builder for method chaining
     */
    template<ColumnConvertible... Args>
    SelectBuilder& select(Args&&... columns) {
        m_columns.clear();
        if constexpr (sizeof...(columns) > 0) {
            (m_columns.push_back(SqlColumn(std::forward<Args>(columns))), ...);
        }
        m_sqlDirty = true;
        return *this;
    }

    /**
     * @brief Select specific columns
     * @param columns The columns to select
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& select(const std::vector<SqlColumn>& columns);

    /**
     * @brief Select all columns (*)
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& selectAll();

    /**
     * @brief Select distinct values
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& distinct();

    /**
     * @brief Specify the FROM clause
     * @param table The table to select from
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& from(const SqlTable& table);

    /**
     * @brief Add a JOIN clause
     * @param table The table to join
     * @param condition The join condition
     * @param joinType The type of join (default: INNER)
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& join(const SqlTable& table, const SqlCondition& condition, SqlJoinType joinType = SqlJoinType::Inner);

    /**
     * @brief Add a LEFT JOIN clause
     * @param table The table to join
     * @param condition The join condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& leftJoin(const SqlTable& table, const SqlCondition& condition);

    /**
     * @brief Add a RIGHT JOIN clause
     * @param table The table to join
     * @param condition The join condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& rightJoin(const SqlTable& table, const SqlCondition& condition);

    /**
     * @brief Add a WHERE condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& where(const SqlCondition& condition);

    /**
     * @brief Add a WHERE condition with a parameter (string-based)
     * @param column The column name
     * @param op The comparison operator (=, <>, >, <, etc.)
     * @param value The value to compare against
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& where(std::string_view column, std::string_view op, const Variant& value);

    /**
     * @brief Add an AND condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& andWhere(const SqlCondition& condition);

    /**
     * @brief Add an OR condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& orWhere(const SqlCondition& condition);

    /**
     * @brief Add a GROUP BY clause with columns
     * @tparam Args Types of the columns (must be convertible to SqlColumn)
     * @param columns The columns to group by
     * @return Reference to this builder for method chaining
     */
    template<ColumnConvertible... Args>
    SelectBuilder& groupBy(Args&&... columns) {
        m_groupByColumns.clear();
        if constexpr (sizeof...(columns) > 0) {
            (m_groupByColumns.push_back(SqlColumn(std::forward<Args>(columns))), ...);
        }
        m_sqlDirty = true;
        return *this;
    }

    /**
     * @brief Add a GROUP BY clause with multiple columns
     * @param columns The columns to group by
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& groupBy(const std::vector<SqlColumn>& columns);

    /**
     * @brief Add a HAVING clause
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& having(const SqlCondition& condition);

    /**
     * @brief Add an ORDER BY clause for a single column
     * @param column The column to order by
     * @param order The sort order (default: ASC)
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& orderBy(const SqlColumn& column, SqlSortOrder order = SqlSortOrder::Ascending);

    /**
     * @brief Add an ORDER BY clause for multiple columns using variadic templates
     * @tparam Args Types of the columns (must be convertible to std::pair<SqlColumn, SqlSortOrder>)
     * @param columnOrders The columns and their sort orders
     * @return Reference to this builder for method chaining
     */
    template<OrderByConvertible... Args>
    SelectBuilder& orderBy(Args&&... columnOrders) {
        if constexpr (sizeof...(columnOrders) > 0) {
            (addOrderByClause(std::forward<Args>(columnOrders)), ...);
        }
        return *this;
    }

    /**
     * @brief Add a LIMIT clause
     * @param limit The maximum number of rows to return
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& limit(int limit);

    /**
     * @brief Add an OFFSET clause
     * @param offset The number of rows to skip
     * @return Reference to this builder for method chaining
     */
    SelectBuilder& offset(int offset);

    /**
     * @brief Build the SQL query string
     * @return The SQL query string
     */
    [[nodiscard]] std::string build() const override;

private:
    // Helper method to add an order by clause from a pair
    template<typename T>
    void addOrderByClause(T&& columnOrder) {
        if constexpr (std::is_convertible_v<T, OrderByClause>) {
            m_orderByClauses.push_back(std::forward<T>(columnOrder));
        } else if constexpr (std::is_convertible_v<T, std::pair<SqlColumn, SqlSortOrder>>) {
            m_orderByClauses.push_back(OrderByClause{
                .column = columnOrder.first,
                .order = columnOrder.second
            });
        }

        m_sqlDirty = true;
    }

    // Helper method to add a where clause
    void addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator);

    std::vector<SqlColumn> m_columns;

    // SqlTable information
    std::optional<SqlTable> m_table;

    // Query options
    bool m_isDistinct;

    // Join clauses
    struct JoinClause {
        SqlTable table;
        SqlCondition condition;
        SqlJoinType joinType;
    };
    std::vector<JoinClause> m_joins;

    // WHERE conditions
    struct WhereClause {
        SqlCondition condition;
        SqlLogicalOperator logicalOperator;
        bool isFirst;
    };
    std::vector<WhereClause> m_whereConditions;

    // GROUP BY columns
    std::vector<SqlColumn> m_groupByColumns;

    // HAVING clause
    std::optional<SqlCondition> m_havingCondition;

    // ORDER BY clauses
    struct OrderByClause {
        SqlColumn column;
        SqlSortOrder order;
    };
    std::vector<OrderByClause> m_orderByClauses;

    // LIMIT and OFFSET
    int m_limit;
    int m_offset;

    // Helper method to build the SQL query
    void buildSql() const;
};

} // namespace database

#endif // DATABASE_SELECT_BUILDER_H
