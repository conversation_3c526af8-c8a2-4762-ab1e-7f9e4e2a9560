﻿#ifndef DATABASE_SQLITE_ERROR_MAPPER_H
#define DATABASE_SQLITE_ERROR_MAPPER_H

#include <string>
#include <string_view>
#include <sqlite3.h>

#include "sql_enums.h"

namespace database {

/**
 * @brief Maps a SQLite error code to our custom ErrorCode enum
 * 
 * This function takes a SQLite error code (like SQLITE_BUSY, SQLITE_ERROR, etc.)
 * and returns the appropriate ErrorCode enum value.
 * 
 * @param sqliteErrorCode The SQLite error code
 * @return The corresponding ErrorCode enum value
 */
[[nodiscard]] constexpr ErrorCode mapSqliteErrorToErrorCode(int sqliteErrorCode) {
    switch (sqliteErrorCode) {
    case SQLITE_OK:
        return ErrorCode::Unknown; // No error
        
    case SQLITE_ERROR:
        return ErrorCode::SyntaxError; // SQL syntax error
        
    case SQLITE_INTERNAL:
        return ErrorCode::InternalError; // Internal SQLite error
        
    case SQLITE_PERM:
    case SQLITE_READONLY:
        return ErrorCode::ResourceError; // Permission or read-only error
        
    case SQLITE_ABORT:
    case SQLITE_INTERRUPT:
        return ErrorCode::OperationCancelled; // Operation was interrupted
        
    case SQLITE_BUSY:
    case SQLITE_LOCKED:
        return ErrorCode::DatabaseLocked; // Database is locked
        
    case SQLITE_NOMEM:
        return ErrorCode::OutOfMemory; // Out of memory
        
    case SQLITE_IOERR:
        return ErrorCode::ResourceError; // I/O error
        
    case SQLITE_CORRUPT:
        return ErrorCode::ResourceError; // Database is corrupt
        
    case SQLITE_NOTFOUND:
        return ErrorCode::ResourceError; // Table or record not found
        
    case SQLITE_FULL:
        return ErrorCode::DiskFull; // Disk full
        
    case SQLITE_CANTOPEN:
        return ErrorCode::ConnectionFailed; // Unable to open database
        
    case SQLITE_PROTOCOL:
        return ErrorCode::NetworkError; // Protocol error
        
    case SQLITE_EMPTY:
        return ErrorCode::ResourceError; // Database is empty
        
    case SQLITE_SCHEMA:
        return ErrorCode::StatementInvalid; // Schema changed
        
    case SQLITE_TOOBIG:
        return ErrorCode::InvalidArgument; // String or BLOB too big
        
    case SQLITE_CONSTRAINT:
        return ErrorCode::ConstraintViolation; // General constraint violation
        
    case SQLITE_MISMATCH:
        return ErrorCode::InvalidArgument; // Data type mismatch
        
    case SQLITE_MISUSE:
        return ErrorCode::InvalidArgument; // Library misuse
        
    case SQLITE_NOLFS:
        return ErrorCode::NotImplemented; // Large file support disabled
        
    case SQLITE_AUTH:
        return ErrorCode::AuthenticationFailed; // Authorization denied
        
    case SQLITE_FORMAT:
        return ErrorCode::ResourceError; // Auxiliary database format error
        
    case SQLITE_RANGE:
        return ErrorCode::InvalidArgument; // Parameter out of range
        
    case SQLITE_NOTADB:
        return ErrorCode::ConnectionFailed; // File is not a database
        
    case SQLITE_NOTICE:
    case SQLITE_WARNING:
        return ErrorCode::Unknown; // Notifications, not errors
        
    case SQLITE_ROW:
    case SQLITE_DONE:
        return ErrorCode::Unknown; // Not errors, but step results
        
    // case SQLITE_TIMEOUT:
    //    return ErrorCode::QueryTimeout; // Query timeout
        
    // Extended error codes for constraints
    case SQLITE_CONSTRAINT_UNIQUE:
        return ErrorCode::UniqueConstraintViolation;
        
    case SQLITE_CONSTRAINT_FOREIGNKEY:
        return ErrorCode::ForeignKeyConstraintViolation;
        
    case SQLITE_CONSTRAINT_CHECK:
        return ErrorCode::CheckConstraintViolation;
        
    case SQLITE_CONSTRAINT_NOTNULL:
        return ErrorCode::NotNullConstraintViolation;
        
    default:
        // For any other error code, return a general execution failure
        return ErrorCode::ExecutionFailed;
    }
}

/**
 * @brief Gets a descriptive message for a SQLite error code
 * 
 * @param sqliteErrorCode The SQLite error code
 * @param db The SQLite database handle (optional)
 * @return A descriptive error message
 */
[[nodiscard]] inline std::string getSqliteErrorMessage(int sqliteErrorCode, sqlite3* db = nullptr) {
    // Get the error message from the database if available
    std::string_view sqliteMessage;
    if (db) {
        sqliteMessage = sqlite3_errmsg(db);
    }
    
    // Get a generic description based on the error code
    std::string_view description;
    switch (sqliteErrorCode) {
    case SQLITE_ERROR:
        description = "SQL syntax error";
        break;
    case SQLITE_BUSY:
        description = "Database is locked";
        break;
    case SQLITE_CONSTRAINT:
        description = "Constraint violation";
        break;
    case SQLITE_NOMEM:
        description = "Out of memory";
        break;
    case SQLITE_READONLY:
        description = "Database is read-only";
        break;
    case SQLITE_INTERRUPT:
        description = "Operation was interrupted";
        break;
    case SQLITE_IOERR:
        description = "I/O error";
        break;
    case SQLITE_FULL:
        description = "Disk full";
        break;
    case SQLITE_CANTOPEN:
        description = "Unable to open database file";
        break;
    case SQLITE_PROTOCOL:
        description = "Database protocol error";
        break;
    case SQLITE_SCHEMA:
        description = "Database schema has changed";
        break;
    case SQLITE_TOOBIG:
        description = "String or BLOB exceeds size limit";
        break;
    case SQLITE_CONSTRAINT_UNIQUE:
        description = "Unique constraint violation";
        break;
    case SQLITE_CONSTRAINT_FOREIGNKEY:
        description = "Foreign key constraint violation";
        break;
    case SQLITE_CONSTRAINT_CHECK:
        description = "Check constraint violation";
        break;
    case SQLITE_CONSTRAINT_NOTNULL:
        description = "Not null constraint violation";
        break;
    default:
        description = "SQLite error";
        break;
    }
    
    // Combine the description with the SQLite message if available
    if (!sqliteMessage.empty()) {
        return std::string{description} + ": " + std::string{sqliteMessage};
    }
    
    return std::string{description};
}

} // namespace database

#endif // DATABASE_SQLITE_ERROR_MAPPER_H
