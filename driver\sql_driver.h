﻿#ifndef DATABASE_SQL_DRIVER_H
#define DATABASE_SQL_DRIVER_H

#include <memory>
#include <string_view>
#include <vector>

#include "connection/connection_params.h"
#include "exception/sql_error.h"

namespace database {

// Forward declarations
class SqlStatement;
class SqlDatabase;

/**
 * @brief Enumeration of database driver features
 */
enum class DriverFeature {
    Transactions,
    BatchOperations,
    PreparedStatements,
    NamedParameters,
    StoredProcedures,
    Schemas,
    MultipleResultSets,
    GeneratedKeys,
    CursorSupport,
    TransactionIsolation,
    SavePoints
};

/**
 * @brief Transaction isolation levels
 */
enum class TransactionIsolation {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable
};

/**
 * @brief Interface for database drivers
 *
 * This class defines the entry point for database drivers.
 * Each driver must implement this interface to provide access to
 * database-specific functionality.
 */
class SqlDriver {
public:
    /**
     * @brief Connection validation result
     */
    enum class ValidationResult {
        Valid,              ///< Connection is valid and ready to use
        Invalid,            ///< Connection is invalid and should be discarded
        Recoverable         ///< Connection is invalid but can be recovered
    };

    virtual ~SqlDriver() = default;

    /**
     * @brief Connect to the database
     * @param params Connection parameters
     * @return True if successful, false otherwise
     */
    virtual bool connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from the database
     * @return True if successful, false otherwise
     */
    virtual bool disconnect() = 0;

    /**
     * @brief Check if connected to the database
     * @return True if connected, false otherwise
     */
    [[nodiscard]] virtual bool isConnected() const = 0;

    /**
     * @brief Validate a database connection
     * @param query The validation query (optional)
     * @return The validation result
     */
    virtual ValidationResult validateConnection(std::string_view query = "") = 0;

    /**
     * @brief Create a statement for executing SQL
     * @return A shared pointer to a statement
     */
    [[nodiscard]] virtual std::shared_ptr<SqlStatement> createStatement() = 0;

    /**
     * @brief Begin a transaction
     * @param level The transaction isolation level
     * @return True if successful, false otherwise
     */
    virtual bool beginTransaction(TransactionIsolation level = TransactionIsolation::ReadCommitted) = 0;

    /**
     * @brief Commit the current transaction
     * @return True if successful, false otherwise
     */
    virtual bool commitTransaction() = 0;

    /**
     * @brief Rollback the current transaction
     * @return True if successful, false otherwise
     */
    virtual bool rollbackTransaction() = 0;

    /**
     * @brief Create a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool createSavepoint(std::string_view name) = 0;

    /**
     * @brief Rollback to a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool rollbackToSavepoint(std::string_view name) = 0;

    /**
     * @brief Release a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool releaseSavepoint(std::string_view name) = 0;

    /**
     * @brief Get connection metadata
     * @return The connection metadata
     */
    [[nodiscard]] virtual ConnectionMetadata metadata() const = 0;

    /**
     * @brief Get the driver name
     * @return The driver name
     */
    [[nodiscard]] virtual std::string_view name() const = 0;

    /**
     * @brief Get the driver version
     * @return The driver version
     */
    [[nodiscard]] virtual std::string_view version() const = 0;

    /**
     * @brief Ping the database to check if the connection is still alive
     * @return True if the connection is alive, false otherwise
     */
    [[nodiscard]] virtual bool ping() = 0;

    /**
     * @brief Get the last error message
     * @return The error message
     */
    [[nodiscard]] virtual const SqlError& lastError() const = 0;

    /**
     * @brief Check if a feature is supported
     * @param feature The feature to check
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsFeature(DriverFeature feature) const = 0;

    /**
     * @brief Get the list of supported features
     * @return The list of supported features
     */
    [[nodiscard]] virtual std::vector<DriverFeature> getSupportedFeatures() const = 0;

    /**
     * @brief Get the maximum number of connections supported by the driver
     * @return The maximum number of connections, or 0 if unlimited
     */
    [[nodiscard]] virtual int getMaxConnections() const { return 0; }

    /**
     * @brief Get the default transaction isolation level
     * @return The default transaction isolation level
     */
    [[nodiscard]] virtual TransactionIsolation getDefaultTransactionIsolation() const {
        return TransactionIsolation::ReadCommitted;
    }

    /**
     * @brief Check if the driver supports multiple open connections
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsMultipleConnections() const { return true; }

    /**
     * @brief Check if the driver supports connection pooling
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsConnectionPooling() const { return true; }

    /**
     * @brief Check if the driver supports connection validation
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsConnectionValidation() const { return true; }

    /**
     * @brief Get the default validation query
     * @return The default validation query
     */
    [[nodiscard]] virtual std::string_view getDefaultValidationQuery() const { return "SELECT 1"; }

    /**
     * @brief Check if the driver is thread-safe
     * @return True if thread-safe, false otherwise
     */
    [[nodiscard]] virtual bool isThreadSafe() const { return true; }
};

} // namespace database

#endif // DATABASE_SQL_DRIVER_H
