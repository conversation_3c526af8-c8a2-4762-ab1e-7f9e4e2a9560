#include "connection_pool.h"
#include "../logging/database_logger.h"
#include <iostream>

namespace database {

ConnectionPool::ConnectionPool(
    const std::string& driverName,
    const ConnectionParams& params,
    size_t initialSize,
    size_t maxSize,
    std::chrono::milliseconds timeout)
    : m_driverName(driverName),
    m_params(params),
    m_initialSize(initialSize),
    m_maxSize(maxSize),
    m_timeout(timeout),
    m_closed(false) {

    // Set default validator
    m_validator = [](std::shared_ptr<SqlDatabase> connection) {
        return connection && connection->isConnected();
    };

    // Set default initializer
    m_initializer = [](std::shared_ptr<SqlDatabase> connection) {
        return connection && connection->connect();
    };

    // Create initial connections
    for (size_t i = 0; i < initialSize; ++i) {
        auto connection = createConnection();
        if (connection) {
            m_idleConnections.push(connection);
        }
    }

    std::cout << "Created connection pool for driver '" << driverName
              << "' with " << m_idleConnections.size() << " initial connections" << std::endl;

    // Log creation
    DatabaseLogger::instance().log(
        LogLevel::Info,
        "Created connection pool for driver '" + driverName + "' with " +
            std::to_string(m_idleConnections.size()) + " initial connections",
        "ConnectionPool");
}

ConnectionPool::~ConnectionPool() {
    close();
}

std::shared_ptr<SqlDatabase> ConnectionPool::getConnection() {
    std::unique_lock<std::mutex> lock(m_mutex);

    if (m_closed) {
        std::cerr << "Connection pool is closed" << std::endl;
        return nullptr;
    }

    // Wait for an available connection or timeout
    bool timedOut = !m_condition.wait_for(lock, m_timeout, [this] {
        return !m_idleConnections.empty() || m_activeConnections.size() < m_maxSize || m_closed;
    });

    if (m_closed) {
        std::cerr << "Connection pool was closed while waiting" << std::endl;
        return nullptr;
    }

    if (timedOut) {
        std::cerr << "Timed out waiting for a connection" << std::endl;

        // Log timeout
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Timed out waiting for a connection",
            "ConnectionPool");

        return nullptr;
    }

    std::shared_ptr<SqlDatabase> connection;

    // Try to get an idle connection
    if (!m_idleConnections.empty()) {
        connection = m_idleConnections.front();
        m_idleConnections.pop();

        // Validate the connection
        if (!validateConnection(connection)) {
            // Connection is invalid, create a new one
            connection = createConnection();
            if (!connection) {
                return nullptr;
            }
        }
    } else if (m_activeConnections.size() < m_maxSize) {
        // Create a new connection
        connection = createConnection();
        if (!connection) {
            return nullptr;
        }
    } else {
        // This should not happen due to the condition variable
        std::cerr << "No available connections" << std::endl;

        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "No available connections",
            "ConnectionPool");

        return nullptr;
    }

    // Add to active connections
    m_activeConnections[connection.get()] = connection;

    return connection;
}

void ConnectionPool::releaseConnection(std::shared_ptr<SqlDatabase> connection) {
    if (!connection) {
        return;
    }

    std::unique_lock<std::mutex> lock(m_mutex);

    // Check if the connection is active
    auto it = m_activeConnections.find(connection.get());
    if (it == m_activeConnections.end()) {
        std::cerr << "Connection not found in active connections" << std::endl;
        return;
    }

    // Remove from active connections
    m_activeConnections.erase(it);

    // If the pool is closed, don't add the connection back
    if (m_closed) {
        return;
    }

    // Validate the connection
    if (validateConnection(connection)) {
        // Add to idle connections
        m_idleConnections.push(connection);

        // Notify waiting threads
        m_condition.notify_one();
    }
}

size_t ConnectionPool::getActiveConnectionCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_activeConnections.size();
}

size_t ConnectionPool::getIdleConnectionCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_idleConnections.size();
}

size_t ConnectionPool::getTotalConnectionCount() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_activeConnections.size() + m_idleConnections.size();
}

void ConnectionPool::setConnectionValidator(std::function<bool(std::shared_ptr<SqlDatabase>)> validator) {
    if (validator) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_validator = validator;
    }
}

void ConnectionPool::setConnectionInitializer(std::function<bool(std::shared_ptr<SqlDatabase>)> initializer) {
    if (initializer) {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_initializer = initializer;
    }
}

void ConnectionPool::close() {
    std::unique_lock<std::mutex> lock(m_mutex);

    if (m_closed) {
        return;
    }

    m_closed = true;

    // Clear idle connections
    while (!m_idleConnections.empty()) {
        auto connection = m_idleConnections.front();
        m_idleConnections.pop();

        if (connection) {
            connection->disconnect();
        }
    }

    // Wait for active connections to be released
    m_condition.notify_all();

    std::cout << "Closed connection pool for driver '" << m_driverName
              << "', " << m_activeConnections.size() << " active connections remain" << std::endl;

    // Log closure
    DatabaseLogger::instance().log(
        LogLevel::Info,
        "Closed connection pool for driver '" + m_driverName + "', " +
            std::to_string(m_activeConnections.size()) + " active connections remain",
        "ConnectionPool");
}

std::shared_ptr<SqlDatabase> ConnectionPool::createConnection() {
    // Create a database connection using SqlDatabase's static methods
    auto connection = std::make_shared<SqlDatabase>(m_driverName);

    if (!connection || !connection->isValid()) {
        std::cerr << "Failed to create connection for driver '" << m_driverName << "'" << std::endl;

        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Failed to create connection for driver '" + m_driverName + "'",
            "ConnectionPool");

        return nullptr;
    }

    // Set connection parameters
    connection->setDatabaseName(m_params.databaseName);
    connection->setUserName(m_params.userName);
    connection->setPassword(m_params.password);
    connection->setHostName(m_params.hostName);
    connection->setPort(m_params.port);
    connection->setConnectOptions(m_params.options);

    // Initialize the connection
    if (!initializeConnection(connection)) {
        std::cerr << "Failed to initialize connection for driver '" << m_driverName << "'" << std::endl;

        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Failed to initialize connection for driver '" + m_driverName + "'",
            "ConnectionPool");

        return nullptr;
    }

    return connection;
}

bool ConnectionPool::validateConnection(std::shared_ptr<SqlDatabase> connection) {
    if (!connection) {
        return false;
    }

    try {
        return m_validator(connection);
    } catch (const std::exception& e) {
        std::cerr << "Error validating connection: " << e.what() << std::endl;

        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Error validating connection: " + std::string(e.what()),
            "ConnectionPool");

        return false;
    }
}

bool ConnectionPool::initializeConnection(std::shared_ptr<SqlDatabase> connection) {
    if (!connection) {
        return false;
    }

    try {
        return m_initializer(connection);
    } catch (const std::exception& e) {
        std::cerr << "Error initializing connection: " << e.what() << std::endl;

        // Log error
        DatabaseLogger::instance().log(
            LogLevel::Error,
            "Error initializing connection: " + std::string(e.what()),
            "ConnectionPool");

        return false;
    }
}

} // namespace database
