#ifndef DATABASE_SQL_OBJECT_H
#define DATABASE_SQL_OBJECT_H

#include <string>
#include <string_view>
#include <memory>
#include <optional>
#include <functional>
#include <type_traits>

#include "sql_enums.h"

namespace database {

// Forward declarations
class SqlDriver;
class SqlDatabase;

/**
 * @brief Type trait for SQL object validation
 */
template<typename T>
struct is_sql_object_like : std::false_type {};

// Specialization for SqlObject and derived classes
template<>
struct is_sql_object_like<class SqlObject> : std::true_type {};

template<typename T>
constexpr bool is_sql_object_like_v = is_sql_object_like<T>::value;

/**
 * @brief Base class for all SQL database objects with modern C++ optimizations
 *
 * This class provides the foundation for all SQL objects with enhanced features:
 * - Type safety through SFINAE and type traits
 * - Optimized move semantics
 * - Better integration with standard algorithms
 * - Enhanced database connectivity
 */
class SqlObject {
public:
    // Constructors with optimizations
    SqlObject() noexcept = default;
    explicit SqlObject(std::string_view name) noexcept;
    SqlObject(std::string_view name, SqlObjectType type) noexcept;
    virtual ~SqlObject() = default;

    // Enhanced copy and move semantics
    SqlObject(const SqlObject& other) = default;
    SqlObject(SqlObject&& other) noexcept = default;
    SqlObject& operator=(const SqlObject& other) = default;
    SqlObject& operator=(SqlObject&& other) noexcept = default;

    // Core Properties with optimizations
    [[nodiscard]] std::string_view name() const noexcept { return m_name; }
    void setName(std::string_view name) noexcept;

    [[nodiscard]] SqlObjectType objectType() const noexcept { return m_type; }
    [[nodiscard]] bool isValid() const noexcept { return !m_name.empty(); }
    [[nodiscard]] bool isEmpty() const noexcept { return m_name.empty(); }

    // Virtual interface for SQL generation
    [[nodiscard]] virtual std::string qualifiedName() const = 0;
    [[nodiscard]] virtual std::string toSql() const = 0;

    // Enhanced comparison operators
    [[nodiscard]] bool operator==(const SqlObject& other) const noexcept {
        return m_name == other.m_name && m_type == other.m_type;
    }

    [[nodiscard]] bool operator!=(const SqlObject& other) const noexcept {
        return !(*this == other);
    }

    [[nodiscard]] bool operator<(const SqlObject& other) const noexcept {
        if (m_name != other.m_name) return m_name < other.m_name;
        return m_type < other.m_type;
    }

    // Hash support for unordered containers
    [[nodiscard]] size_t hash() const noexcept {
        return std::hash<std::string>{}(m_name) ^
               (static_cast<size_t>(m_type) << 1);
    }

protected:
    void setObjectType(SqlObjectType type) noexcept { m_type = type; }

    // Enhanced database integration
    [[nodiscard]] virtual std::shared_ptr<SqlDatabase> database() const { return nullptr; }
    virtual void setDatabase(std::shared_ptr<SqlDatabase> db) {}

    // Metadata management
    [[nodiscard]] virtual bool hasMetadata() const noexcept { return false; }
    virtual bool loadMetadata() { return false; }
    virtual bool refreshMetadata() { return false; }

private:
    std::string m_name;
    SqlObjectType m_type = SqlObjectType::Unknown;
};

} // namespace database

// Hash specialization for SqlObject to use in unordered containers
namespace std {
template<>
struct hash<database::SqlObject> {
    [[nodiscard]] size_t operator()(const database::SqlObject& obj) const noexcept {
        return obj.hash();
    }
};
} // namespace std

#endif // DATABASE_SQL_OBJECT_H
