#ifndef DATABASE_SQL_OBJECT_H
#define DATABASE_SQL_OBJECT_H

#include <string>
#include <string_view>

#include "sql_enums.h"

namespace database {

// Forward declarations
class SqlDriver;

class SqlObject {
public:
    SqlObject() = default;
    explicit SqlObject(std::string_view name);
    SqlObject(std::string_view name, SqlObjectType type);
    virtual ~SqlObject() = default;

    // Copy and move semantics
    SqlObject(const SqlObject& other) = default;
    SqlObject(SqlObject&& other) noexcept = default;
    SqlObject& operator=(const SqlObject& other) = default;
    SqlObject& operator=(SqlObject&& other) noexcept = default;

    // Basic Properties
    [[nodiscard]] std::string_view name() const noexcept;
    void setName(std::string_view name);

    [[nodiscard]] SqlObjectType objectType() const noexcept { return m_type; }
    [[nodiscard]] bool isValid() const noexcept { return !m_name.empty(); }

    [[nodiscard]] virtual std::string qualifiedName() const = 0;
    [[nodiscard]] virtual std::string toSql() const = 0;

protected:
    void setObjectType(SqlObjectType type) noexcept { m_type = type; }

    // virtual bool createInDatabase(SqlConflictResolution conflictResolution = SqlConflictResolution::PreferLocal);
    // virtual bool loadFromDatabase();
    // virtual bool dropFromDatabase();
    // virtual bool synchronize(SqlConflictResolution conflictResolution = SqlConflictResolution::Manual);
    // [[nodiscard]] virtual std::string generateCreateSql() const = 0;
    // [[nodiscard]] virtual std::string generateDropSql() const;
    // [[nodiscard]] virtual std::string generateAlterSql() const;
    // // Synchronization State Management
    // [[nodiscard]] SqlSyncState syncState() const noexcept;
    // void setSyncState(SqlSyncState newState);

    // [[nodiscard]] virtual bool existsInDatabase() const;
    // [[nodiscard]] bool hasLocalChanges() const noexcept;
    // // Error Handling
    // [[nodiscard]] const SqlError& lastError() const noexcept;
    // void clearError() noexcept;
    // void setError(const SqlError& error) noexcept;
    // void setError(std::string_view message, ErrorCode code, std::string_view sqlState = "");
    // bool executeSql(std::string_view sql);

    // void markAsModified();
    // void clearModified();

private:
    std::string m_name;
    SqlObjectType m_type = SqlObjectType::Unknown;
};

} // namespace database

#endif // DATABASE_SQL_OBJECT_H
