#include "sql_row.h"

#include <sstream>
#include <stdexcept>

#include "sql_table.h"
#include "sql_database.h"
#include "sql_query.h"
#include "exception/sql_error.h"

namespace database {

/**
 * @brief Private implementation class for SqlRow
 *
 * This class provides the database interaction layer (Tier 3) for SqlRow.
 * It manages the actual database operations and maintains connection state.
 */
class SqlRowPrivate
{
public:
    SqlRowPrivate() = default;
    ~SqlRowPrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Associated table
    SqlTable table;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(std::string_view message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlRow::SqlRow(std::string_view tableName)
    : SqlObject(tableName, SqlObjectType::Row) {
    // Initialize metadata with table information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->tableName = tableName;
}

SqlRow::SqlRow(std::string_view tableName, DataMap values)
    : SqlObject(tableName, SqlObjectType::Row)
    , m_values(std::move(values)) {
    // Initialize metadata with table information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->tableName = tableName;

    // Update column names from values
    updateColumnNames();
}

SqlRow::SqlRow(std::string_view tableName, DataMap&& values) noexcept
    : SqlObject(tableName, SqlObjectType::Row)
    , m_values(std::move(values)) {
    // Initialize metadata with table information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->tableName = tableName;

    // Update column names from values
    updateColumnNames();
}

SqlRow::SqlRow(DataMap values)
    : SqlObject("", SqlObjectType::Row)
    , m_values(std::move(values)) {

    // Update column names from values
    updateColumnNames();
}

SqlRow::SqlRow(DataMap&& values) noexcept
    : SqlObject("", SqlObjectType::Row)
    , m_values(std::move(values)) {

    // Update column names from values
    updateColumnNames();
}

// Copy constructor
SqlRow::SqlRow(const SqlRow& other)
    : SqlObject(other)
    , m_values(other.m_values)
    , m_metadata(other.m_metadata)
    , d_ptr(other.d_ptr) {
}

// Copy assignment operator
SqlRow& SqlRow::operator=(const SqlRow& other) {
    if (this != &other) {
        SqlObject::operator=(other);
        m_values = other.m_values;
        m_metadata = other.m_metadata;
        d_ptr = other.d_ptr;
    }
    return *this;
}

// Move constructor
SqlRow::SqlRow(SqlRow&& other) noexcept
    : SqlObject(std::move(other))
    , m_values(std::move(other.m_values))
    , m_metadata(std::move(other.m_metadata))
    , d_ptr(std::move(other.d_ptr)) {
}

// Move assignment operator
SqlRow& SqlRow::operator=(SqlRow&& other) noexcept {
    if (this != &other) {
        SqlObject::operator=(std::move(other));
        m_values = std::move(other.m_values);
        m_metadata = std::move(other.m_metadata);
        d_ptr = std::move(other.d_ptr);
    }
    return *this;
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlRow SqlRow::fromDatabase(std::shared_ptr<SqlTable> table,
                            std::string_view whereClause,
                            bool loadMetadata) {
    if (!table) {
        return SqlRow{};
    }

    SqlRow row(table->name());

    // Set up database connection
    auto db = table->database();
    if (db) {
        if (!row.d_ptr) {
            row.d_ptr = std::make_shared<SqlRowPrivate>();
        }
        row.d_ptr->setDatabase(db);
        row.d_ptr->table = *table;

        if (loadMetadata) {
            // Load row data from database
            try {
                SqlQuery query(*db);
                std::string sql = "SELECT * FROM " + table->qualifiedName();

                if (!whereClause.empty()) {
                    sql += " WHERE " + std::string(whereClause);
                }
                sql += " LIMIT 1";

                query.setQuery(sql);

                if (query.execute() && query.next()) {
                    // Get all field names and values
                    auto record = query.record();
                    auto fieldNames = record.fieldNames();

                    for (const auto& fieldName : fieldNames) {
                        row.m_values[fieldName] = record.value(fieldName);
                    }

                    row.updateColumnNames();
                }
            } catch (const std::exception&) {
                // Ignore errors during loading
            }
        }
    }

    return row;
}

//----------------------------------------------------------------------
// Row Metadata
//----------------------------------------------------------------------

std::shared_ptr<SqlRow::SqlRowMetadata> SqlRow::metadata() const {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRow::SqlRowMetadata>();
    }
    return m_metadata;
}

void SqlRow::setMetadata(const SqlRowMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    *m_metadata = metadata;
}

//----------------------------------------------------------------------
// Value Access Methods
//----------------------------------------------------------------------

Data SqlRow::value(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        return it->second;
    }
    return Data{}; // Return empty Data if not found
}

Data SqlRow::value(size_t index) const {
    if (!m_metadata || index >= m_metadata->columnNames.size()) {
        throw std::out_of_range("Column index out of range");
    }

    auto& columnName = m_metadata->columnNames[index];
    return value(columnName);
}

void SqlRow::setValue(std::string_view columnName, Data value) {
    m_values[std::string(columnName)] = std::move(value);

    // Mark as dirty
    if (m_metadata) {
        m_metadata->isDirty = true;
    }

    // Update column names if this is a new column
    updateColumnNames();
}

void SqlRow::setValue(size_t index, Data value) {
    if (!m_metadata || index >= m_metadata->columnNames.size()) {
        throw std::out_of_range("Column index out of range");
    }

    auto& columnName = m_metadata->columnNames[index];
    setValue(columnName, std::move(value));
}

const DataMap& SqlRow::values() const {
    return m_values;
}

void SqlRow::setValues(DataMap values) {
    m_values = std::move(values);

    // Mark as dirty
    if (m_metadata) {
        m_metadata->isDirty = true;
    }

    // Update column names
    updateColumnNames();
}

//----------------------------------------------------------------------
// Column Management Methods
//----------------------------------------------------------------------

bool SqlRow::hasColumn(std::string_view columnName) const {
    return m_values.find(std::string(columnName)) != m_values.end();
}

size_t SqlRow::columnCount() const {
    return m_values.size();
}

std::vector<std::string> SqlRow::columnNames() const {
    if (m_metadata && !m_metadata->columnNames.empty()) {
        return m_metadata->columnNames;
    }

    // Extract column names from values map
    std::vector<std::string> names;
    names.reserve(m_values.size());
    for (const auto& pair : m_values) {
        names.push_back(pair.first);
    }
    return names;
}

void SqlRow::removeColumn(std::string_view columnName) {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        m_values.erase(it);

        // Mark as dirty
        if (m_metadata) {
            m_metadata->isDirty = true;
        }

        // Update column names
        updateColumnNames();
    }
}

bool SqlRow::isEmpty() const {
    return m_values.empty();
}

void SqlRow::clear() {
    m_values.clear();

    // Mark as dirty
    if (m_metadata) {
        m_metadata->isDirty = true;
        m_metadata->columnNames.clear();
    }
}

//----------------------------------------------------------------------
// State Management Methods
//----------------------------------------------------------------------

bool SqlRow::isDirty() const {
    return m_metadata ? m_metadata->isDirty : false;
}

void SqlRow::setDirty(bool dirty) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->isDirty = dirty;
}

bool SqlRow::isDeleted() const {
    return m_metadata ? m_metadata->isDeleted : false;
}

void SqlRow::setDeleted(bool deleted) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->isDeleted = deleted;
}

std::optional<size_t> SqlRow::rowId() const {
    return m_metadata ? m_metadata->rowId : std::nullopt;
}

void SqlRow::setRowId(std::optional<size_t> rowId) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->rowId = rowId;
}

std::optional<std::string> SqlRow::version() const {
    return m_metadata ? m_metadata->version : std::nullopt;
}

void SqlRow::setVersion(std::optional<std::string> version) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->version = std::move(version);
}

std::string_view SqlRow::tableName() const {
    if (m_metadata && !m_metadata->tableName.empty()) {
        return m_metadata->tableName;
    }
    return name();
}

void SqlRow::setTableName(std::string tableName) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->tableName = std::move(tableName);

    // Also update the object name
    setName(m_metadata->tableName);
}

//----------------------------------------------------------------------
// Operator Overloads
//----------------------------------------------------------------------

Data& SqlRow::operator[](std::string_view columnName) {
    return getValueRef(columnName);
}

const Data& SqlRow::operator[](std::string_view columnName) const {
    return getValueRef(columnName);
}

Data& SqlRow::operator[](size_t index) {
    return getValueRef(index);
}

const Data& SqlRow::operator[](size_t index) const {
    return getValueRef(index);
}

//----------------------------------------------------------------------
// Table Association
//----------------------------------------------------------------------

SqlTable SqlRow::table() const noexcept {
    if (d_ptr) {
        return d_ptr->table;
    }

    // Return a table constructed from metadata
    if (m_metadata && !m_metadata->tableName.empty()) {
        return SqlTable(m_metadata->tableName);
    }

    return SqlTable{};
}

void SqlRow::setTable(const SqlTable& table) noexcept {
    // Initialize d_ptr if needed
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlRowPrivate>();
    }

    d_ptr->table = table;

    // Update metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }
    m_metadata->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        d_ptr->setDatabase(db);
    }
}

std::shared_ptr<SqlDatabase> SqlRow::database() const {
    if (d_ptr && d_ptr->database) {
        return d_ptr->database;
    }

    // Try to get database from associated table
    auto tbl = table();
    return tbl.database();
}

//----------------------------------------------------------------------
// Database Operations
//----------------------------------------------------------------------

bool SqlRow::save() {
    if (isEmpty()) {
        return false;
    }

    // Determine whether to insert or update based on row state
    if (rowId().has_value() || !isDirty()) {
        return update();
    } else {
        return insert();
    }
}

bool SqlRow::insert() {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build INSERT statement
        std::string sql = toInsertStatement();

        query.setQuery(sql).prepare();

        // Bind values
        size_t paramIndex = 1;
        for (const auto& pair : m_values) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to insert row: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->isDirty = false;
            m_metadata->insertTime = std::chrono::system_clock::now();

            // Try to get the inserted row ID
            // Get the last insert row ID from the database
            SqlQuery idQuery(*d_ptr->database);
            idQuery.setQuery("SELECT last_insert_rowid()").prepare();
            std::optional<int64_t> lastInsertId;
            if (idQuery.execute() && idQuery.next()) {
                lastInsertId = idQuery.value(0).to<int64_t>();
            }
            if (lastInsertId.has_value()) {
                m_metadata->rowId = static_cast<size_t>(lastInsertId.value());
            }
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception inserting row: " + std::string(e.what()));
        return false;
    }
}

bool SqlRow::update(std::string_view whereClause) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build UPDATE statement
        std::string sql = toUpdateStatement(whereClause.empty() ? buildDefaultWhereClause() : whereClause);

        query.setQuery(sql).prepare();

        // Bind field values
        size_t paramIndex = 1;
        for (const auto& pair : m_values) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        // Bind WHERE clause parameters if using default where clause
        if (whereClause.empty() && rowId().has_value()) {
            query.bind(static_cast<int>(paramIndex), static_cast<int64_t>(rowId().value()));
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to update row: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->isDirty = false;
            m_metadata->updateTime = std::chrono::system_clock::now();
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception updating row: " + std::string(e.what()));
        return false;
    }
}

bool SqlRow::deleteFromDatabase(std::string_view whereClause) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DELETE statement
        std::string sql = "DELETE FROM " + std::string(tableName());

        std::string actualWhereClause = whereClause.empty() ? buildDefaultWhereClause() : std::string(whereClause);
        if (!actualWhereClause.empty()) {
            sql += " WHERE " + actualWhereClause;
        }

        query.setQuery(sql).prepare();

        // Bind WHERE clause parameters if using default where clause
        if (whereClause.empty() && rowId().has_value()) {
            query.bind(1, static_cast<int64_t>(rowId().value()));
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to delete row: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->isDeleted = true;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception deleting row: " + std::string(e.what()));
        return false;
    }
}

bool SqlRow::reload(std::string_view whereClause) {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT statement
        std::string sql = "SELECT * FROM " + std::string(tableName());

        std::string actualWhereClause = whereClause.empty() ? buildDefaultWhereClause() : std::string(whereClause);
        if (!actualWhereClause.empty()) {
            sql += " WHERE " + actualWhereClause;
        }
        sql += " LIMIT 1";

        query.setQuery(sql).prepare();

        // Bind WHERE clause parameters if using default where clause
        if (whereClause.empty() && rowId().has_value()) {
            query.bind(1, static_cast<int64_t>(rowId().value()));
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        // Clear existing values and load new ones
        m_values.clear();

        auto record = query.record();
        auto fieldNames = record.fieldNames();

        for (const auto& fieldName : fieldNames) {
            m_values[fieldName] = record.value(fieldName);
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->isDirty = false;
        }

        updateColumnNames();
        return true;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlRow::exists(std::string_view whereClause) const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT COUNT statement
        std::string sql = "SELECT COUNT(*) FROM " + std::string(tableName());

        auto actualWhereClause = whereClause.empty() ? buildDefaultWhereClause() : std::string(whereClause);
        if (!actualWhereClause.empty()) {
            sql += " WHERE " + actualWhereClause;
        }

        query.setQuery(sql).prepare();

        // Bind WHERE clause parameters if using default where clause
        if (whereClause.empty() && rowId().has_value()) {
            query.bind(1, static_cast<int64_t>(rowId().value()));
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlRow::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }

    // Reload from database if possible
    if (d_ptr && d_ptr->isValid()) {
        return reload();
    }

    return false;
}

//----------------------------------------------------------------------
// Validation Methods
//----------------------------------------------------------------------

bool SqlRow::isValid() const {
    auto errors = validationErrors();
    return errors.empty();
}

std::vector<std::string> SqlRow::validationErrors() const {
    std::vector<std::string> errors;

    // Check if table name is set
    if (tableName().empty()) {
        errors.push_back("Table name is not set");
    }

    // Check if row has any values
    if (m_values.empty()) {
        errors.push_back("Row has no values");
    }

    // Validate individual columns
    for (const auto& pair : m_values) {
        if (!isColumnValid(pair.first)) {
            errors.push_back("Invalid value for column: " + pair.first);
        }
    }

    return errors;
}

bool SqlRow::isColumnValid(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it == m_values.end()) {
        return false;
    }

    // Basic validation - check if value is not null for required columns
    // This could be enhanced with table schema validation
    const auto& value = it->second;

    // For now, just check if the value is valid (not corrupted)
    try {
        // Try to convert to string to check validity
        value.to<std::string>();
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlRow::qualifiedName() const {
    return std::string(tableName()) + " (row)";
}

std::string SqlRow::toSql() const {
    return toInsertStatement();
}

std::string SqlRow::toJson() const {
    std::ostringstream json;
    json << "{";

    bool first = true;
    for (const auto& pair : m_values) {
        if (!first)
            json << ", ";
        first = false;

        json << "\"" << pair.first << "\": ";

        // Format value based on type
        if (pair.second.isString()) {
            json << "\"" << pair.second.to<std::string>() << "\"";
        } else if (pair.second.isNull()) {
            json << "null";
        } else {
            json << pair.second.to<std::string>();
        }
    }

    json << "}";
    return json.str();
}

std::string SqlRow::toInsertStatement(std::string_view tableName) const {
    auto actualTableName = tableName.empty() ? this->tableName() : tableName;

    if (m_values.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "INSERT INTO " << actualTableName << " (";

    // Add column names
    bool first = true;
    for (const auto& pair : m_values) {
        if (!first)
            sql << ", ";
        first = false;
        sql << pair.first;
    }

    sql << ") VALUES (";

    // Add parameter placeholders
    first = true;
    for (const auto& pair : m_values) {
        if (!first)
            sql << ", ";
        first = false;
        sql << "?";
    }

    sql << ")";
    return sql.str();
}

std::string SqlRow::toUpdateStatement(std::string_view whereClause, std::string_view tableName) const {
    auto actualTableName = tableName.empty() ? this->tableName() : tableName;

    if (m_values.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "UPDATE " << actualTableName << " SET ";

    // Add SET clauses
    bool first = true;
    for (const auto& pair : m_values) {
        if (!first)
            sql << ", ";
        first = false;
        sql << pair.first << " = ?";
    }

    // Add WHERE clause
    if (!whereClause.empty()) {
        sql << " WHERE " << whereClause;
    }

    return sql.str();
}

//----------------------------------------------------------------------
// Helper Methods
//----------------------------------------------------------------------

void SqlRow::ensureDatabase() const {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlRowPrivate>();
    }
}

void SqlRow::updateColumnNames() {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlRowMetadata>();
    }

    // Update column names from values map
    m_metadata->columnNames.clear();
    m_metadata->columnNames.reserve(m_values.size());

    for (const auto& pair : m_values) {
        m_metadata->columnNames.push_back(pair.first);
    }
}

Data& SqlRow::getValueRef(std::string_view columnName) {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        return it->second;
    }
    auto colName = std::string(columnName);
    // Create new entry if not found
    m_values[colName] = Data{};

    // Mark as dirty and update column names
    if (m_metadata) {
        m_metadata->isDirty = true;
    }
    updateColumnNames();

    return m_values[colName];
}

const Data& SqlRow::getValueRef(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        return it->second;
    }

    // Return a static empty Data for const access
    static const Data emptyData{};
    return emptyData;
}

Data& SqlRow::getValueRef(size_t index) {
    if (!m_metadata || index >= m_metadata->columnNames.size()) {
        throw std::out_of_range("Column index out of range");
    }

    auto& columnName = m_metadata->columnNames[index];
    return getValueRef(columnName);
}

const Data& SqlRow::getValueRef(size_t index) const {
    if (!m_metadata || index >= m_metadata->columnNames.size()) {
        throw std::out_of_range("Column index out of range");
    }

    auto& columnName = m_metadata->columnNames[index];
    return getValueRef(columnName);
}

std::string SqlRow::buildDefaultWhereClause() const {
    if (rowId().has_value()) {
        return "id = ?";  // Assuming 'id' is the primary key column
    }

    // If no row ID, build WHERE clause from all values
    if (m_values.empty()) {
        return "";
    }

    std::ostringstream where;
    bool first = true;
    for (const auto& pair : m_values) {
        if (!first) where << " AND ";
        first = false;
        where << pair.first << " = ?";
    }

    return where.str();
}

} // namespace database
