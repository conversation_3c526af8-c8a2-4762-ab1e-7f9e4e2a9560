#ifndef DATABASE_SQL_COLUMN_H
#define DATABASE_SQL_COLUMN_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>
#include <span>
#include <ranges>
#include <algorithm>
#include <type_traits>

#include "sql_object.h"
#include "sql_types.h"

namespace database {

// Forward declarations
class SqlColumnPrivate;
class SqlTable;
class SqlDatabase;
class SqlCondition;

/**
 * @brief Type trait for column-like objects
 */
template<typename T>
struct is_column_like {
    template<typename U>
    static auto test(int) -> decltype(
        std::declval<const U&>().dataType(),
        std::declval<const U&>().tableName(),
        std::true_type{}
    );

    template<typename>
    static std::false_type test(...);

    static constexpr bool value = decltype(test<T>(0))::value;
};

template<typename T>
constexpr bool is_column_like_v = is_column_like<T>::value;

/**
 * @brief Class representing a database column
 *
 * This class serves as both a database column definer and accessor:
 * - Definer Mode: Define column properties for table creation/modification
 * - Accessor Mode: Access existing column metadata and generate conditions
 *
 * The class implements synchronization between local column definition
 * and actual database column metadata when connected to a database.
 */
class SqlColumn final : public SqlObject {
public:
    /**
     * @brief Column constraint information
     */
    /*struct Constraint {
        SqlConstraintType type = SqlConstraintType::None;
        std::string name;
        Variant value;
        std::string referencedTable;
        std::string referencedColumn;
        // std::vector<std::string> referencedColumns;

        Constraint(SqlConstraintType t, std::string_view n = "", const Variant& v = Variant())
            : type(t), name(n), value(v) {}
    };*/

    /**
     * @brief Enhanced column metadata structure with modern C++ optimizations
     */
    struct SqlColumnMetadata {
        SqlDataType dataType = SqlDataType::Unknown;                 ///< Column data type
        std::string tableName;                                       ///< Parent table name
        std::string comment;                                         ///< Column comment/description
        size_t maxLength = 0;                                        ///< Maximum length for string types
        std::optional<uint8_t> precision;                            ///< Precision for numeric types
        std::optional<uint8_t> scale;                                ///< Scale for decimal types
        SqlColumnConstraint constraints = SqlColumnConstraint::None; ///< Column constraints
        std::optional<Data> defaultValue;                            ///< Default value
        std::string checkConstraint;                                 ///< Check constraint expression
        std::string referencedTable;                                 ///< Referenced table for foreign keys
        std::string referencedColumn;                                ///< Referenced column for foreign keys
        std::string onUpdateAction;                                  ///< ON UPDATE action for foreign keys
        std::string onDeleteAction;                                  ///< ON DELETE action for foreign keys
        std::string charset;                                         ///< Character set for string types
        std::string collation;                                       ///< Collation for string types
        int ordinalPosition = 0;                                     ///< Position in table (1-based)
        bool isNullable = true;                                      ///< Can contain NULL values
        bool isAutoIncrement = false;                                ///< Is auto increment
        bool isGenerated = false;                                    ///< Is generated column
        std::string generationExpression;                            ///< Generation expression
        std::unordered_map<std::string, std::string> properties;     ///< Additional properties

        // Enhanced constructors with perfect forwarding
        SqlColumnMetadata() = default;

        template<typename TableName, typename Comment = std::string>
        SqlColumnMetadata(TableName&& tableName, SqlDataType dataType, Comment&& comment = {})
            : dataType(dataType)
            , tableName(std::forward<TableName>(tableName))
            , comment(std::forward<Comment>(comment)) {}

        // Utility methods for constraint checking
        [[nodiscard]] bool hasConstraint(SqlColumnConstraint constraint) const noexcept {
            return (constraints & constraint) != SqlColumnConstraint::None;
        }

        void addConstraint(SqlColumnConstraint constraint) noexcept {
            constraints = constraints | constraint;
        }

        void removeConstraint(SqlColumnConstraint constraint) noexcept {
            constraints = constraints & ~constraint;
        }

        // Type checking utilities
        [[nodiscard]] bool isNumericType() const noexcept {
            return dataType == SqlDataType::Integer || dataType == SqlDataType::BigInt ||
                   dataType == SqlDataType::SmallInt || dataType == SqlDataType::TinyInt ||
                   dataType == SqlDataType::Real || dataType == SqlDataType::Float ||
                   dataType == SqlDataType::Double || dataType == SqlDataType::Decimal ||
                   dataType == SqlDataType::Numeric;
        }

        [[nodiscard]] bool isStringType() const noexcept {
            return dataType == SqlDataType::Varchar || dataType == SqlDataType::Char ||
                   dataType == SqlDataType::Text;
        }

        [[nodiscard]] bool isDateTimeType() const noexcept {
            return dataType == SqlDataType::Date || dataType == SqlDataType::Time ||
                   dataType == SqlDataType::DateTime || dataType == SqlDataType::Timestamp;
        }
    };

    // Enhanced constructors with modern C++ features
    SqlColumn() noexcept;
    explicit SqlColumn(std::string_view name, SqlDataType type = SqlDataType::Unknown) noexcept;
    SqlColumn(std::string_view name, const SqlTable& table) noexcept;

    // Template constructor for type-safe column creation
    template<typename T, typename = std::enable_if_t<std::is_convertible_v<T, Data>>>
    SqlColumn(std::string_view name, const SqlTable& table, T&& defaultValue)
        : SqlColumn(name, table) {
        if (auto meta = metadata()) {
            meta->defaultValue = Data(std::forward<T>(defaultValue));
        }
    }

    // Enhanced copy/move operations with optimizations
    SqlColumn(const SqlColumn& other) = default;
    SqlColumn& operator=(const SqlColumn& other) = default;
    SqlColumn(SqlColumn&& other) noexcept = default;
    SqlColumn& operator=(SqlColumn&& other) noexcept = default;

    //----------------------------------------------------------------------
    // Static Factory Methods with Enhanced Type Safety
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlColumn fromDatabase(std::string_view name, SqlDatabase* db);
    [[nodiscard]] static SqlColumn fromField(const class SqlField& field);

    // Modern factory methods with perfect forwarding
    template<typename... Args>
    [[nodiscard]] static SqlColumn create(std::string_view name, SqlDataType type, Args&&... args) {
        SqlColumn column(name, type);
        if constexpr (sizeof...(args) > 0) {
            auto meta = column.metadata();
            if (meta) {
                // Apply additional metadata using fold expressions
                (column.applyMetadataArg(std::forward<Args>(args)), ...);
            }
        }
        return column;
    }

    // Utility factory methods for common column types
    [[nodiscard]] static SqlColumn integer(std::string_view name, bool autoIncrement = false) {
        auto column = SqlColumn(name, SqlDataType::Integer);
        if (autoIncrement && column.metadata()) {
            column.metadata()->addConstraint(SqlColumnConstraint::AutoIncrement);
        }
        return column;
    }

    [[nodiscard]] static SqlColumn varchar(std::string_view name, size_t maxLength = 255) {
        auto column = SqlColumn(name, SqlDataType::Varchar);
        if (column.metadata()) {
            column.metadata()->maxLength = maxLength;
        }
        return column;
    }

    [[nodiscard]] static SqlColumn primaryKey(std::string_view name, SqlDataType type = SqlDataType::Integer) {
        auto column = SqlColumn(name, type);
        if (column.metadata()) {
            column.metadata()->addConstraint(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::NotNull);
        }
        return column;
    }

    //----------------------------------------------------------------------
    // Enhanced Metadata Management with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] SqlColumnMetadata* metadata() const noexcept;
    void setMetadata(const SqlColumnMetadata& metadata);

    // Metadata lifecycle with enhanced error handling
    bool loadMetadata() override;
    bool refreshMetadata() override;
    [[nodiscard]] bool hasMetadata() const noexcept override;

    // Core properties with optimized accessors
    [[nodiscard]] SqlDataType dataType() const noexcept;
    SqlColumn& setDataType(SqlDataType type) noexcept;

    [[nodiscard]] std::string_view tableName() const noexcept;
    SqlColumn& setTableName(std::string tableName);

    [[nodiscard]] std::string_view comment() const noexcept;
    SqlColumn& setComment(std::string comment);

    [[nodiscard]] size_t maxLength() const noexcept;
    SqlColumn& setMaxLength(size_t length) noexcept;

    [[nodiscard]] std::optional<uint8_t> precision() const noexcept;
    SqlColumn& setPrecision(uint8_t precision) noexcept;

    [[nodiscard]] std::optional<uint8_t> scale() const noexcept;
    SqlColumn& setScale(uint8_t scale) noexcept;

    // Enhanced constraint management with fluent interface
    [[nodiscard]] SqlColumnConstraint constraints() const noexcept;
    SqlColumn& setConstraints(SqlColumnConstraint constraints) noexcept;
    SqlColumn& addConstraint(SqlColumnConstraint constraint) noexcept;
    SqlColumn& removeConstraint(SqlColumnConstraint constraint) noexcept;
    [[nodiscard]] bool hasConstraint(SqlColumnConstraint constraint) const noexcept;

    // Default value management with type safety
    [[nodiscard]] std::optional<Data> defaultValue() const noexcept;
    SqlColumn& setDefaultValue(const Data& value);

    template<typename T>
    SqlColumn& setDefaultValue(T&& value) {
        static_assert(std::is_convertible_v<T, Data>, "Value must be convertible to Data");
        return setDefaultValue(Data(std::forward<T>(value)));
    }

    // Boolean properties with fluent interface
    [[nodiscard]] bool isNullable() const noexcept;
    SqlColumn& setNullable(bool nullable = true) noexcept;
    SqlColumn& notNull() noexcept { return setNullable(false); }

    [[nodiscard]] bool isAutoIncrement() const noexcept;
    SqlColumn& setAutoIncrement(bool autoIncrement = true) noexcept;
    SqlColumn& autoIncrement() noexcept { return setAutoIncrement(true); }

    [[nodiscard]] bool isPrimaryKey() const noexcept;
    SqlColumn& setPrimaryKey(bool primaryKey = true) noexcept;
    SqlColumn& primaryKey() noexcept { return setPrimaryKey(true); }

    [[nodiscard]] bool isUnique() const noexcept;
    SqlColumn& setUnique(bool unique = true) noexcept;
    SqlColumn& unique() noexcept { return setUnique(true); }

    [[nodiscard]] int ordinalPosition() const noexcept;
    SqlColumn& setOrdinalPosition(int position) noexcept;

    // [[nodiscard]] const std::string& nativeTypeName() const;
    // SqlColumn& foreignKey(std::string_view referencedTable, std::string_view referencedColumn);
    // SqlColumn& check(std::string_view condition);

    //----------------------------------------------------------------------
    // Enhanced Table Association with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    SqlColumn& setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    SqlColumn& setDatabase(std::shared_ptr<SqlDatabase> db) override;

    // Database operations with enhanced error handling
    [[nodiscard]] bool exists() const;
    bool create(std::optional<int> position = std::nullopt);
    bool drop();
    bool modify();

    // Enhanced data analysis with modern C++ features
    template<typename Container = std::vector<Data>>
    [[nodiscard]] Container distinctValues(std::optional<size_t> limit = std::nullopt) const {
        static_assert(std::is_same_v<typename Container::value_type, Data>,
                     "Container must hold Data values");
        auto values = getDistinctValuesImpl(limit);
        if constexpr (std::is_same_v<Container, std::vector<Data>>) {
            return values;
        } else {
            return Container(values.begin(), values.end());
        }
    }

    [[nodiscard]] std::optional<Data> minValue() const;
    [[nodiscard]] std::optional<Data> maxValue() const;
    [[nodiscard]] std::pair<std::optional<Data>, std::optional<Data>> minMaxValues() const;
    [[nodiscard]] size_t nonNullCount() const;
    [[nodiscard]] size_t nullCount() const;
    [[nodiscard]] size_t totalCount() const { return nonNullCount() + nullCount(); }

    // Enhanced SQL generation
    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    //----------------------------------------------------------------------
    // Enhanced SQL Generation with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] std::string definitionSql() const;
    [[nodiscard]] std::string createSql() const;
    [[nodiscard]] std::string dropSql() const;
    [[nodiscard]] std::string alterSql() const;
    [[nodiscard]] std::string constraintSql() const;

    // Enhanced condition generation with fluent interface
    [[nodiscard]] SqlCondition operator==(const Variant& value) const;
    [[nodiscard]] SqlCondition operator!=(const Variant& value) const;
    [[nodiscard]] SqlCondition operator<(const Variant& value) const;
    [[nodiscard]] SqlCondition operator<=(const Variant& value) const;
    [[nodiscard]] SqlCondition operator>(const Variant& value) const;
    [[nodiscard]] SqlCondition operator>=(const Variant& value) const;

    // Modern condition methods with enhanced type safety
    template<typename T>
    [[nodiscard]] SqlCondition equals(T&& value) const {
        return eq(Variant(std::forward<T>(value)));
    }

    template<typename T>
    [[nodiscard]] SqlCondition notEquals(T&& value) const {
        return neq(Variant(std::forward<T>(value)));
    }

    template<typename Container>
    [[nodiscard]] SqlCondition in(const Container& values) const {
        std::vector<Variant> variants;
        variants.reserve(values.size());
        std::transform(values.begin(), values.end(), std::back_inserter(variants),
                      [](const auto& val) { return Variant(val); });
        return inImpl(variants);
    }

    [[nodiscard]] SqlCondition like(std::string_view pattern) const;
    [[nodiscard]] SqlCondition notLike(std::string_view pattern) const;
    [[nodiscard]] SqlCondition between(const Variant& min, const Variant& max) const;
    [[nodiscard]] SqlCondition isNull() const;
    [[nodiscard]] SqlCondition isNotNull() const;

private:
    // Helper methods for metadata management
    template<typename T>
    void applyMetadataArg(T&& arg) {
        if constexpr (std::is_same_v<std::decay_t<T>, SqlColumnConstraint>) {
            addConstraint(std::forward<T>(arg));
        } else if constexpr (std::is_convertible_v<T, Data>) {
            setDefaultValue(std::forward<T>(arg));
        }
        // Add more metadata argument types as needed
    }

    // Implementation methods
    [[nodiscard]] std::vector<Data> getDistinctValuesImpl(std::optional<size_t> limit) const;
    [[nodiscard]] SqlCondition eq(const Variant& value) const;
    [[nodiscard]] SqlCondition neq(const Variant& value) const;
    [[nodiscard]] SqlCondition lt(const Variant& value) const;
    [[nodiscard]] SqlCondition lte(const Variant& value) const;
    [[nodiscard]] SqlCondition gt(const Variant& value) const;
    [[nodiscard]] SqlCondition gte(const Variant& value) const;
    [[nodiscard]] SqlCondition inImpl(const std::vector<Variant>& values) const;
    [[nodiscard]] SqlCondition notIn(const std::vector<Variant>& values) const;

    // Enhanced member variables with better encapsulation
    mutable std::shared_ptr<SqlColumnMetadata> m_metadata { nullptr };
    mutable std::shared_ptr<SqlColumnPrivate> d_ptr { nullptr };
};

} // namespace database

// Enhanced hash specialization for SqlColumn with modern C++ features
namespace std {
template<>
struct hash<database::SqlColumn> {
    [[nodiscard]] size_t operator()(const database::SqlColumn& column) const noexcept {
        // Use the base SqlObject hash combined with table information
        size_t h1 = column.hash();

        // Hash the table name if available
        size_t h2 = 0;
        try {
            auto tableName = column.tableName();
            if (!tableName.empty()) {
                h2 = std::hash<std::string_view>{}(tableName);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the data type for additional uniqueness
        size_t h3 = static_cast<size_t>(column.dataType());

        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};
} // namespace std

#endif // DATABASE_SQL_COLUMN_H
