#ifndef DATABASE_SQL_COLUMN_H
#define DATABASE_SQL_COLUMN_H

#include <string>
#include <string_view>
#include <memory>
#include <vector>
#include <optional>

#include "sql_object.h"
#include "sql_types.h"

namespace database {

// Forward declarations
class SqlColumnPrivate;
class SqlTable;
class SqlDatabase;
class SqlCondition;

/**
 * @brief Class representing a database column
 *
 * This class serves as both a database column definer and accessor:
 * - Definer Mode: Define column properties for table creation/modification
 * - Accessor Mode: Access existing column metadata and generate conditions
 *
 * The class implements synchronization between local column definition
 * and actual database column metadata when connected to a database.
 */
class SqlColumn final : public SqlObject {
public:
    /**
     * @brief Column constraint information
     */
    /*struct Constraint {
        SqlConstraintType type = SqlConstraintType::None;
        std::string name;
        Variant value;
        std::string referencedTable;
        std::string referencedColumn;
        // std::vector<std::string> referencedColumns;

        Constraint(SqlConstraintType t, std::string_view n = "", const Variant& v = Variant())
            : type(t), name(n), value(v) {}
    };*/

    /**
     * @brief Simple column metadata structure
     */
    struct SqlColumnMetadata {
        SqlDataType dataType = SqlDataType::Unknown;                 ///< Column data type
        std::string tableName;                                       ///< Parent table name
        std::string comment;                                         ///< Column comment/description
        size_t maxLength = 0;                             ///< Maximum length for string types
        std::optional<uint8_t> precision;                            ///< Precision for numeric types
        std::optional<uint8_t> scale;                                ///< Scale for decimal types
        SqlColumnConstraint constraints = SqlColumnConstraint::None; ///< Column constraints
        std::optional<Data> defaultValue;                            ///< Default value
        std::string checkConstraint;                                 ///< Check constraint expression
        std::string referencedTable;                                 ///< Referenced table for foreign keys
        std::string referencedColumn;                                ///< Referenced column for foreign keys
        std::string onUpdateAction;                                  ///< ON UPDATE action for foreign keys
        std::string onDeleteAction;                                  ///< ON DELETE action for foreign keys
        std::string charset;                                         ///< Character set for string types
        std::string collation;                                       ///< Collation for string types
        int ordinalPosition = 0;                                     ///< Position in table (1-based)
        bool isNullable = true;                                      ///< Can contain NULL values
        bool isAutoIncrement = false;                                ///< Is auto increment
        bool isGenerated = false;                                    ///< Is generated column
        std::string generationExpression;                            ///< Generation expression
        std::unordered_map<std::string, std::string> properties;     ///< Additional properties

        // std::optional<int> size;
        SqlColumnMetadata() = default;
        SqlColumnMetadata(std::string tableName, SqlDataType dataType, std::string comment = {})
            : dataType(dataType), tableName(std::move(tableName)), comment(std::move(comment)) {}
    };

    SqlColumn();
    SqlColumn(std::string_view name, SqlDataType type = SqlDataType::Unknown);
    SqlColumn(std::string_view name, const SqlTable& table);

    // Copy/move operations
    SqlColumn(const SqlColumn& other) = default;
    SqlColumn& operator=(const SqlColumn& other) = default;
    SqlColumn(SqlColumn&& other) noexcept = default;
    SqlColumn& operator=(SqlColumn&& other) noexcept = default;

    //----------------------------------------------------------------------
    // Static Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlColumn fromDatabase(std::string_view name, SqlDatabase* db);
    [[nodiscard]] static SqlColumn fromField(const class SqlField& field);

    //----------------------------------------------------------------------
    // Table Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] SqlColumnMetadata* metadata() const noexcept;
    void setMetadata(const SqlColumnMetadata& metadata);

    bool loadMetadata();
    bool refreshMetadata();

    [[nodiscard]] SqlDataType dataType() const noexcept;
    void setDataType(SqlDataType type);
    [[nodiscard]] std::string tableName() const;
    void setTableName(std::string tableName);
    [[nodiscard]] std::string comment() const;
    void setComment(std::string comment);
    [[nodiscard]] size_t maxLength() const;
    void setMaxLength(size_t length);
    [[nodiscard]] std::optional<uint8_t> precision() const;
    void setPrecision(uint8_t precision);
    [[nodiscard]] std::optional<uint8_t> scale() const;
    void setScale(uint8_t scale);
    [[nodiscard]] SqlColumnConstraint constraints() const;
    void setConstraints(SqlColumnConstraint constraints);
    void addConstraint(SqlColumnConstraint constraint);
    void removeConstraint(SqlColumnConstraint constraint);
    [[nodiscard]] bool hasConstraint(SqlColumnConstraint constraint) const;
    [[nodiscard]] std::optional<Data> defaultValue() const;
    void setDefaultValue(const Data& value);
    [[nodiscard]] bool isNullable() const;
    void setNullable(bool nullable);
    [[nodiscard]] bool isAutoIncrement() const;
    void setAutoIncrement(bool autoIncrement);
    [[nodiscard]] bool isPrimaryKey() const;
    void setPrimaryKey(bool primaryKey);
    [[nodiscard]] bool isUnique() const;
    void setUnique(bool unique);
    [[nodiscard]] int ordinalPosition() const;
    void setOrdinalPosition(int position);

    // [[nodiscard]] const std::string& nativeTypeName() const;
    // SqlColumn& foreignKey(std::string_view referencedTable, std::string_view referencedColumn);
    // SqlColumn& check(std::string_view condition);

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    void setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;

    [[nodiscard]] bool exists() const;
    bool create(std::optional<int> position = std::nullopt);
    bool drop();
    bool modify();

    [[nodiscard]] std::vector<Data> distinctValues(std::optional<size_t> limit = std::nullopt) const;
    [[nodiscard]] std::optional<Data> minValue() const;
    [[nodiscard]] std::optional<Data> maxValue() const;
    [[nodiscard]] size_t nonNullCount() const;
    [[nodiscard]] size_t nullCount() const;

    [[nodiscard]] std::string qualifiedName() const;
    [[nodiscard]] std::string toSql() const;

    //----------------------------------------------------------------------
    // SqlObject Implementation
    //----------------------------------------------------------------------
    std::string definitionSql() const;
    [[nodiscard]] std::string createSql() const;
    [[nodiscard]] std::string dropSql() const;
    [[nodiscard]] std::string alterSql() const;
    [[nodiscard]] std::string constraintSql() const;

private:
    // SqlColumn& as(std::string_view alias) noexcept;
    [[nodiscard]] SqlCondition eq(const Variant& value) const;
    [[nodiscard]] SqlCondition neq(const Variant& value) const;
    [[nodiscard]] SqlCondition lt(const Variant& value) const;
    [[nodiscard]] SqlCondition lte(const Variant& value) const;
    [[nodiscard]] SqlCondition gt(const Variant& value) const;
    [[nodiscard]] SqlCondition gte(const Variant& value) const;
    [[nodiscard]] SqlCondition like(std::string_view pattern) const;
    [[nodiscard]] SqlCondition notLike(std::string_view pattern) const;
    [[nodiscard]] SqlCondition in(const std::vector<Variant>& values) const;
    [[nodiscard]] SqlCondition notIn(const std::vector<Variant>& values) const;
    [[nodiscard]] SqlCondition between(const Variant& min, const Variant& max) const;
    [[nodiscard]] SqlCondition isNull() const;
    [[nodiscard]] SqlCondition isNotNull() const;
    [[nodiscard]] SqlCondition condition(std::string_view op, const Variant& value) const;
    [[nodiscard]] SqlCondition condition(std::string_view op) const;
    [[nodiscard]] SqlCondition condition(std::string_view op, const std::vector<Variant>& values) const;

private:
    mutable std::shared_ptr<SqlColumnMetadata> m_metadata { nullptr };
    mutable std::shared_ptr<SqlColumnPrivate> d_ptr { nullptr };
};

} // namespace database

// Hash function for SqlColumn to use it as a key in unordered_map
/*namespace std {
template<>
struct hash<database::SqlColumn> {
    [[nodiscard]] size_t operator()(const database::SqlColumn& column) const noexcept {
        // Hash the column name
        size_t h1 = std::hash<std::string>{}(column.name());

        // Hash the table if it exists
        size_t h2 = 0;
        if (column.hasTable()) {
            // Calculate table hash directly
            const auto& table = column.table();
            size_t tableNameHash = std::hash<std::string>{}(table->name());
            size_t tableAliasHash = table->hasAlias() ? std::hash<std::string>{}(table->alias()) : 0;
            h2 = tableNameHash ^ (tableAliasHash << 1);
        }

        // Hash the alias if it exists
        size_t h3 = column.hasAlias() ? std::hash<std::string>{}(column.alias()) : 0;

        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};
}*/

#endif // DATABASE_SQL_COLUMN_H
