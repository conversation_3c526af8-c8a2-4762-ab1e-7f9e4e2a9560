#include "sql_column.h"

#include <format>
#include <optional>

#include "sql_condition.h"
#include "sql_query.h"
#include "sql_table.h"

namespace database {

/**
 * @brief Private implementation class for SqlColumn
 *
 * This class provides the database interaction layer (Tier 3) for SqlColumn.
 * It manages the actual database operations and maintains connection state.
 */
class SqlColumnPrivate {
public:
    SqlColumnPrivate() = default;
    ~SqlColumnPrivate() = default;

    SqlDatabase* database = nullptr;
    SqlTable table;
    SqlError lastError;
    bool databaseEnabled = false;

    void setDatabase(SqlDatabase* db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Enhanced Constructors and Destructors
//----------------------------------------------------------------------

SqlColumn::SqlColumn() noexcept
    : SqlObject("", SqlObjectType::Column) {
}

SqlColumn::SqlColumn(std::string_view name, SqlDataType type) noexcept
    : SqlObject(name, SqlObjectType::Column) {
    // Initialize metadata with basic information
    m_metadata = std::make_shared<SqlColumnMetadata>();
    m_metadata->dataType = type;
}

SqlColumn::SqlColumn(std::string_view name, const SqlTable& table) noexcept
    : SqlObject(name, SqlObjectType::Column) {
    m_metadata = std::make_shared<SqlColumnMetadata>();
    m_metadata->tableName = table.name();

    // Set up database connection
    auto db = table.database();
    if (db) {
        d_ptr = std::make_shared<SqlColumnPrivate>();
        d_ptr->setDatabase(db.get());
        d_ptr->table = table;
    }
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlColumn SqlColumn::fromDatabase(std::string_view name, SqlDatabase* db) {
    SqlColumn column(name);

    if (!db) {
        return column;
    }

    // Initialize private implementation
    column.d_ptr = std::make_shared<SqlColumnPrivate>();
    column.d_ptr->setDatabase(db);

    return column;
}

SqlColumn SqlColumn::fromField(const SqlField& field) {
    SqlColumn column(field.name());
    column.m_metadata = std::make_shared<SqlColumnMetadata>();

    // Copy field properties
    column.metadata()->dataType = field.type();
    column.metadata()->defaultValue = field.defaultValue();
    column.metadata()->isNullable = !field.isRequired();
    column.metadata()->isAutoIncrement = field.isAutoIncrement();

    if (field.isPrimaryKey()) {
        column.addConstraint(SqlColumnConstraint::PrimaryKey);
    }

    return column;
}

//----------------------------------------------------------------------
// Enhanced Factory Methods
//----------------------------------------------------------------------

SqlColumn SqlColumn::integer(std::string_view name, bool autoIncrement) {
    auto column = SqlColumn(name, SqlDataType::Integer);
    if (autoIncrement && column.metadata()) {
        column.metadata()->addConstraint(SqlColumnConstraint::AutoIncrement);
        column.metadata()->isAutoIncrement = true;
    }
    return column;
}

SqlColumn SqlColumn::varchar(std::string_view name, size_t maxLength) {
    auto column = SqlColumn(name, SqlDataType::Varchar);
    if (column.metadata()) {
        column.metadata()->maxLength = maxLength;
    }
    return column;
}

SqlColumn SqlColumn::primaryKey(std::string_view name, SqlDataType type) {
    auto column = SqlColumn(name, type);
    if (column.metadata()) {
        column.metadata()->addConstraint(SqlColumnConstraint::PrimaryKey | SqlColumnConstraint::NotNull);
        column.metadata()->isNullable = false;
    }
    return column;
}

//----------------------------------------------------------------------
// Table Metadata
//----------------------------------------------------------------------

SqlColumn::SqlColumnMetadata* SqlColumn::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlColumnMetadata>();
    }
    return m_metadata.get();
}

void SqlColumn::setMetadata(const SqlColumnMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlColumnMetadata>();
    }
    *m_metadata = metadata;
}

bool SqlColumn::hasMetadata() const noexcept {
    return m_metadata != nullptr;
}

bool SqlColumn::loadMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        // Query database for column metadata
        SqlQuery query(*d_ptr->database);

        // Build query to get column information
        std::string sql = "SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, m_metadata ? m_metadata->tableName : "");

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to load column metadata: {})", query.lastError().message()));
            return false;
        }

        if (query.next()) {
            // Initialize metadata if not exists
            if (!m_metadata) {
                m_metadata = std::make_shared<SqlColumnMetadata>();
            }

            // Extract metadata from query result
            // This is a simplified implementation - actual column names may vary by database
            m_metadata->tableName = query.value("TABLE_NAME").to<std::string>();
            m_metadata->dataType = SqlDataType::Unknown; // Would need mapping from DB type string
            m_metadata->isNullable = query.value("IS_NULLABLE").to<std::string>() == "YES";
            m_metadata->ordinalPosition = query.value("ORDINAL_POSITION").to<int>();

            // Additional fields as available
            if (!query.value("COLUMN_DEFAULT").isNull()) {
                m_metadata->defaultValue = query.value("COLUMN_DEFAULT");
            }

            d_ptr->clearError();
            return true;
        }

        d_ptr->setError("Column not found in database");
        return false;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception loading metadata: " + std::string(e.what()));
        return false;
    }
}

bool SqlColumn::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

//----------------------------------------------------------------------
// Metadata Accessor Methods
//----------------------------------------------------------------------

SqlDataType SqlColumn::dataType() const noexcept {
    return m_metadata ? m_metadata->dataType : SqlDataType::Unknown;
}

SqlColumn& SqlColumn::setDataType(SqlDataType type) noexcept {
    metadata()->dataType = type;
    return *this;
}

std::string_view SqlColumn::tableName() const noexcept {
    if (m_metadata) {
        return m_metadata->tableName;
    }
    if (d_ptr) {
        return d_ptr->table.name();
    }
    return "";
}

SqlColumn& SqlColumn::setTableName(std::string tableName) {
    metadata()->tableName = std::move(tableName);
    return *this;
}

std::string_view SqlColumn::comment() const noexcept {
    return m_metadata ? std::string_view(m_metadata->comment) : std::string_view("");
}

SqlColumn& SqlColumn::setComment(std::string comment) {
    metadata()->comment = std::move(comment);
    return *this;
}

size_t SqlColumn::maxLength() const noexcept {
    return m_metadata ? m_metadata->maxLength : 0;
}

SqlColumn& SqlColumn::setMaxLength(size_t length) noexcept {
    metadata()->maxLength = length;
    return *this;
}

std::optional<uint8_t> SqlColumn::precision() const noexcept {
    return m_metadata ? m_metadata->precision : std::nullopt;
}

SqlColumn& SqlColumn::setPrecision(uint8_t precision) noexcept {
    metadata()->precision = precision;
    return *this;
}

std::optional<uint8_t> SqlColumn::scale() const noexcept {
    return m_metadata ? m_metadata->scale : std::nullopt;
}

SqlColumn& SqlColumn::setScale(uint8_t scale) noexcept {
    metadata()->scale = scale;
    return *this;
}

SqlColumnConstraint SqlColumn::constraints() const {
    return m_metadata ? m_metadata->constraints : SqlColumnConstraint::None;
}

SqlColumn& SqlColumn::setConstraints(SqlColumnConstraint constraints) noexcept {
    metadata()->constraints = constraints;
    return *this;
}

SqlColumn& SqlColumn::addConstraint(SqlColumnConstraint constraint) noexcept {
    metadata()->constraints = metadata()->constraints | constraint;
    return *this;
}

SqlColumn& SqlColumn::removeConstraint(SqlColumnConstraint constraint) noexcept {
    metadata()->constraints = metadata()->constraints & ~constraint;
    return *this;
}

bool SqlColumn::hasConstraint(SqlColumnConstraint constraint) const noexcept {
    return m_metadata && (m_metadata->constraints & constraint) != SqlColumnConstraint::None;
}

std::optional<Data> SqlColumn::defaultValue() const noexcept {
    return m_metadata ? m_metadata->defaultValue : std::nullopt;
}

SqlColumn& SqlColumn::setDefaultValue(const Data& value) {
    metadata()->defaultValue = value;
    return *this;
}

bool SqlColumn::isNullable() const noexcept {
    if (m_metadata) {
        return m_metadata->isNullable && !hasConstraint(SqlColumnConstraint::NotNull);
    }
    return true; // Default to nullable
}

SqlColumn& SqlColumn::setNullable(bool nullable) noexcept {
    metadata()->isNullable = nullable;

    // Update constraints accordingly
    if (nullable) {
        removeConstraint(SqlColumnConstraint::NotNull);
    } else {
        addConstraint(SqlColumnConstraint::NotNull);
    }
    return *this;
}

bool SqlColumn::isAutoIncrement() const noexcept {
    if (m_metadata) {
        return m_metadata->isAutoIncrement || hasConstraint(SqlColumnConstraint::AutoIncrement);
    }
    return false;
}

SqlColumn& SqlColumn::setAutoIncrement(bool autoIncrement) noexcept {
    metadata()->isAutoIncrement = autoIncrement;

    // Update constraints accordingly
    if (autoIncrement) {
        addConstraint(SqlColumnConstraint::AutoIncrement);
    } else {
        removeConstraint(SqlColumnConstraint::AutoIncrement);
    }
    return *this;
}

bool SqlColumn::isPrimaryKey() const noexcept {
    return hasConstraint(SqlColumnConstraint::PrimaryKey);
}

SqlColumn& SqlColumn::setPrimaryKey(bool primaryKey) noexcept {
    if (primaryKey) {
        addConstraint(SqlColumnConstraint::PrimaryKey);
        // Primary keys are typically not null
        setNullable(false);
    } else {
        removeConstraint(SqlColumnConstraint::PrimaryKey);
    }
    return *this;
}

bool SqlColumn::isUnique() const noexcept {
    return hasConstraint(SqlColumnConstraint::Unique) || isPrimaryKey();
}

SqlColumn& SqlColumn::setUnique(bool unique) noexcept {
    if (unique) {
        addConstraint(SqlColumnConstraint::Unique);
    } else {
        removeConstraint(SqlColumnConstraint::Unique);
    }
    return *this;
}

int SqlColumn::ordinalPosition() const noexcept {
    return m_metadata ? m_metadata->ordinalPosition : -1;
}

SqlColumn& SqlColumn::setOrdinalPosition(int position) noexcept {
    metadata()->ordinalPosition = position;
    return *this;
}

//----------------------------------------------------------------------
// Table Association
//----------------------------------------------------------------------

SqlTable SqlColumn::table() const noexcept {
    if (d_ptr) {
        return d_ptr->table;
    }

    // Return a table constructed from metadata
    if (m_metadata && !m_metadata->tableName.empty()) {
        return SqlTable(m_metadata->tableName);
    }

    return SqlTable{};
}

SqlColumn& SqlColumn::setTable(const SqlTable& table) noexcept {
    // Initialize d_ptr if needed
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlColumnPrivate>();
    }

    d_ptr->table = table;

    // Update metadata
    metadata()->tableName = table.name();

    // Set up database connection if table has one
    auto db = table.database();
    if (db) {
        d_ptr->setDatabase(db.get());
    }
    return *this;
}

std::shared_ptr<SqlDatabase> SqlColumn::database() const {
    if (d_ptr && d_ptr->database) {
        // Return a shared_ptr - this assumes SqlDatabase can be converted to shared_ptr
        // This might need adjustment based on actual SqlDatabase implementation
        return std::shared_ptr<SqlDatabase>(d_ptr->database, [](SqlDatabase*) {});
    }

    // Try to get database from associated table
    auto tbl = table();
    return tbl.database();
}

void SqlColumn::setDatabase(std::shared_ptr<SqlDatabase> db) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlColumnPrivate>();
    }
    d_ptr->setDatabase(db.get());
}

//----------------------------------------------------------------------
// Database Operations
//----------------------------------------------------------------------

bool SqlColumn::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if column exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE COLUMN_NAME = ? AND TABLE_NAME = ?";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata()->tableName);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlColumn::create(std::optional<int> position) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build ALTER TABLE ADD COLUMN statement
        std::string sql = "ALTER TABLE " + metadata()->tableName + " ADD COLUMN " + createSql();

        if (position.has_value()) {
            // Add position clause if supported by database
            // This is MySQL-specific syntax - would need database-specific handling
            sql += " AFTER (SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '"
                   + metadata()->tableName + "' AND ORDINAL_POSITION = " + std::to_string(position.value()) + ")";
        }

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create column: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating column: " + std::string(e.what()));
        return false;
    }
}

bool SqlColumn::drop() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build ALTER TABLE DROP COLUMN statement
        std::string sql = "ALTER TABLE " + metadata()->tableName + " DROP COLUMN " + std::string(name());

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop column: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping column: " + std::string(e.what()));
        return false;
    }
}

bool SqlColumn::modify() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build ALTER TABLE MODIFY COLUMN statement
        std::string sql = "ALTER TABLE " + metadata()->tableName + " MODIFY COLUMN " + definitionSql();

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to modify column: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception modifying column: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Data Analysis Operations
//----------------------------------------------------------------------

std::vector<Data> SqlColumn::getDistinctValuesImpl(std::optional<size_t> limit) const {
    std::vector<Data> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT DISTINCT " + std::string(name()) + " FROM " + metadata()->tableName;
        if (limit.has_value()) {
            sql += " LIMIT " + std::to_string(limit.value());
        }

        query.setQuery(sql);

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            result.push_back(query.value(0));
        }

    } catch (const std::exception&) {
        // Return empty result on error
    }

    return result;
}

std::pair<std::optional<Data>, std::optional<Data>> SqlColumn::minMaxValues() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return {std::nullopt, std::nullopt};
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT MIN(" + std::string(name()) + "), MAX(" + std::string(name()) + ") FROM " + metadata()->tableName;
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return {std::nullopt, std::nullopt};
        }

        auto minVal = query.value(0);
        auto maxVal = query.value(1);

        return {
            minVal.isNull() ? std::nullopt : std::make_optional(minVal),
            maxVal.isNull() ? std::nullopt : std::make_optional(maxVal)
        };

    } catch (const std::exception&) {
        return {std::nullopt, std::nullopt};
    }
}

std::optional<Data> SqlColumn::minValue() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return std::nullopt;
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT MIN(" + std::string(name()) + ") FROM " + metadata()->tableName;
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return std::nullopt;
        }

        auto value = query.value(0);
        return value.isNull() ? std::nullopt : std::make_optional(value);

    } catch (const std::exception&) {
        return std::nullopt;
    }
}

std::optional<Data> SqlColumn::maxValue() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return std::nullopt;
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT MAX(" + std::string(name()) + ") FROM " + metadata()->tableName;
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return std::nullopt;
        }

        auto value = query.value(0);
        return value.isNull() ? std::nullopt : std::make_optional(value);

    } catch (const std::exception&) {
        return std::nullopt;
    }
}

size_t SqlColumn::nonNullCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT COUNT(" + std::string(name()) + ") FROM " + metadata()->tableName;
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlColumn::nullCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        std::string sql = "SELECT COUNT(*) FROM " + metadata()->tableName + " WHERE " + std::string(name()) + " IS NULL";
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlColumn::qualifiedName() const {
    if (d_ptr && !d_ptr->table.name().empty()) {
        return std::format("{}.{}", d_ptr->table.qualifiedName(), name());
    }
    if (m_metadata && !m_metadata->tableName.empty()) {
        return m_metadata->tableName + "." + std::string(name());
    }
    return std::string(name());
}

std::string SqlColumn::toSql() const {
    return qualifiedName();
}

std::string SqlColumn::definitionSql() const {
    std::ostringstream sql;

    // Column name
    sql << name();

    // Data type
    sql << " " << sqlDataTypeToString(metadata()->dataType);

    // Length/precision/scale
    if (metadata()->maxLength > 0) {
        sql << "(" << metadata()->maxLength << ")";
    } else if (metadata()->precision.has_value()) {
        sql << "(" << static_cast<int>(metadata()->precision.value());
        if (metadata()->scale.has_value()) {
            sql << "," << static_cast<int>(metadata()->scale.value());
        }
        sql << ")";
    }

    // Constraints
    if (!metadata()->isNullable) {
        sql << " NOT NULL";
    }

    if (metadata()->isAutoIncrement) {
        sql << " AUTO_INCREMENT";
    }

    if (hasConstraint(SqlColumnConstraint::PrimaryKey)) {
        sql << " PRIMARY KEY";
    } else if (hasConstraint(SqlColumnConstraint::Unique)) {
        sql << " UNIQUE";
    }

    // Default value
    if (metadata()->defaultValue.has_value()) {
        sql << " DEFAULT ";
        const auto& defaultVal = metadata()->defaultValue.value();
        if (defaultVal.isString()) {
            sql << "'" << defaultVal.to<std::string>() << "'";
        } else {
            sql << defaultVal.to<std::string>();
        }
    }

    // Comment
    if (!metadata()->comment.empty()) {
        sql << " COMMENT '" << metadata()->comment << "'";
    }

    return sql.str();
}

std::string SqlColumn::createSql() const {
    std::string sql = std::format("{} {}", name(), sqlDataTypeToString(metadata()->dataType));

    // Add constraints
    if (!metadata()->isNullable) {
        sql += " NOT NULL";
    }

    if (metadata()->isAutoIncrement) {
        sql += " AUTOINCREMENT";
    }

    if (!metadata()->defaultValue->isNull()) {
        sql += std::format(" DEFAULT {}", metadata()->defaultValue->to<std::string>());
    }

    // Add other constraints
    std::string constraintStr = constraintSql();
    if (!constraintStr.empty()) {
        sql += " " + constraintStr;
    }

    return sql;
}

std::string SqlColumn::alterSql() const {
    if (!d_ptr) {
        return "";
    }

    return std::format("ALTER TABLE {} MODIFY COLUMN {}",
                       d_ptr->table.name(), createSql());
}

std::string SqlColumn::dropSql() const {
    return "DROP COLUMN " + std::string(name());
}

std::string SqlColumn::constraintSql() const {
    std::ostringstream sql;

    if (hasConstraint(SqlColumnConstraint::PrimaryKey)) {
        sql << "PRIMARY KEY (" << name() << ")";
    } else if (hasConstraint(SqlColumnConstraint::Unique)) {
        sql << "UNIQUE (" << name() << ")";
    }

    if (hasConstraint(SqlColumnConstraint::ForeignKey)) {
        if (!sql.str().empty()) sql << ", ";
        sql << "FOREIGN KEY (" << name() << ") REFERENCES "
            << metadata()->referencedTable << "(" << metadata()->referencedColumn << ")";

        if (!metadata()->onUpdateAction.empty()) {
            sql << " ON UPDATE " << metadata()->onUpdateAction;
        }
        if (!metadata()->onDeleteAction.empty()) {
            sql << " ON DELETE " << metadata()->onDeleteAction;
        }
    }

    if (hasConstraint(SqlColumnConstraint::Check) && !metadata()->checkConstraint.empty()) {
        if (!sql.str().empty()) sql << ", ";
        sql << "CHECK (" << metadata()->checkConstraint << ")";
    }

    return sql.str();
}

/*std::string SqlColumn::toSql() const {
    if (!metadata()->alias.empty()) {
        return std::format("{} AS {}", qualifiedName(), metadata()->alias);
    }
    return qualifiedName();
}*/

/*bool SqlColumn::setAlias(std::string_view alias) {
    ensureMetadataLoaded();
    if (m_metadata) {
        m_metadata->alias = alias;
        return true;
    }
    return true;
}*/

//----------------------------------------------------------------------
// Enhanced Condition Methods with Operator Overloads
//----------------------------------------------------------------------

SqlCondition SqlColumn::operator==(const Variant& value) const {
    return eq(value);
}

SqlCondition SqlColumn::operator!=(const Variant& value) const {
    return neq(value);
}

SqlCondition SqlColumn::operator<(const Variant& value) const {
    return lt(value);
}

SqlCondition SqlColumn::operator<=(const Variant& value) const {
    return lte(value);
}

SqlCondition SqlColumn::operator>(const Variant& value) const {
    return gt(value);
}

SqlCondition SqlColumn::operator>=(const Variant& value) const {
    return gte(value);
}

SqlCondition SqlColumn::eq(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::Equal, value);
}

SqlCondition SqlColumn::neq(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::NotEqual, value);
}

SqlCondition SqlColumn::lt(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::LessThan, value);
}

SqlCondition SqlColumn::lte(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::LessEqual, value);
}

SqlCondition SqlColumn::gt(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::GreaterThan, value);
}

SqlCondition SqlColumn::gte(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::GreaterEqual, value);
}

SqlCondition SqlColumn::like(std::string_view pattern) const {
    return SqlCondition(*this, SqlOperator::Like, pattern);
}

SqlCondition SqlColumn::notLike(std::string_view pattern) const {
    return SqlCondition(*this, SqlOperator::NotLike, pattern);
}

SqlCondition SqlColumn::inImpl(const std::vector<Variant>& values) const {
    return SqlCondition(*this, SqlOperator::In, values);
}

SqlCondition SqlColumn::notIn(const std::vector<Variant>& values) const {
    return SqlCondition(*this, SqlOperator::NotIn, values);
}

SqlCondition SqlColumn::between(const Variant& min, const Variant& max) const {
    // Create the vector with the right capacity to avoid reallocations
    std::vector<Variant> values;
    values.reserve(2);
    values.push_back(min);
    values.push_back(max);
    return SqlCondition(*this, SqlOperator::Between, values);
}

SqlCondition SqlColumn::isNull() const {
    return SqlCondition(*this, SqlOperator::IsNull);
}

SqlCondition SqlColumn::isNotNull() const {
    return SqlCondition(*this, SqlOperator::IsNotNull);
}

SqlCondition SqlColumn::condition(std::string_view op, const Variant& value) const {
    return SqlCondition(*this, op, value);
}

SqlCondition SqlColumn::condition(std::string_view op) const {
    return SqlCondition(*this, op);
}

SqlCondition SqlColumn::condition(std::string_view op, const std::vector<Variant>& values) const {
    return SqlCondition(*this, op, values);
}

/*std::strong_ordering SqlColumn::operator<=>(const SqlColumn& other) const noexcept {
    // Compare names first
    if (auto cmp = m_name <=> other.m_name; cmp != 0) {
        return cmp;
    }

    // Compare table presence
    if (hasTable() != other.hasTable()) {
        return hasTable() ? std::strong_ordering::greater : std::strong_ordering::less;
    }

    // Compare tables if both present
    if (hasTable() && other.hasTable()) {
        if (*m_table != *other.m_table) {
            // Since SqlTable doesn't have <=> yet, we'll use a simple comparison
            // This could be improved if SqlTable gets a <=> operator
            return m_table->name() < other.m_table->name() ?
                       std::strong_ordering::less : std::strong_ordering::greater;
        }
    }

    return m_metadata <=> other.m_metadata;
}*/

} // namespace database
