#include "sql_table.h"

#include <algorithm>
#include <format>

#include "driver/sql_driver.h"
#include "sql_column.h"
#include "sql_index.h"
#include "sql_query.h"
#include "sql_row.h"

namespace database {

/**
 * @brief Private implementation class for SqlTable
 *
 * This class provides the database interaction layer (Tier 3) for SqlTable.
 * It manages the actual database operations and maintains connection state.
 */
class SqlTablePrivate {
public:
    SqlTablePrivate() = default;
    ~SqlTablePrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isConnected();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Enhanced Constructors and Destructors
//----------------------------------------------------------------------

SqlTable::SqlTable() noexcept
    : SqlObject("", SqlObjectType::Table) {
}

SqlTable::SqlTable(std::string_view name) noexcept
    : SqlObject(name, SqlObjectType::Table) {
}

SqlTable::SqlTable(std::string_view name, std::shared_ptr<SqlDatabase> db)
    : SqlObject(name, SqlObjectType::Table) {
    if (db) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlTablePrivate>();
        }
        d_ptr->setDatabase(db);
    }
}

SqlTable::SqlTable(std::string_view name, std::string_view schema, std::string_view alias) noexcept
    : SqlObject(name, SqlObjectType::Table) {
    // Initialize metadata with schema information
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    m_metadata->schema = schema;
    // Note: alias handling might need to be added to metadata structure
}

SqlTable::~SqlTable() = default;

// Copy constructor
SqlTable::SqlTable(const SqlTable& other)
    : SqlObject(other)
    , m_metadata(other.m_metadata)
    , d_ptr(other.d_ptr) {
}

// Copy assignment operator
SqlTable& SqlTable::operator=(const SqlTable& other) {
    if (this != &other) {
        SqlObject::operator=(other);
        m_metadata = other.m_metadata;
        d_ptr = other.d_ptr;
    }
    return *this;
}

// Move constructor
SqlTable::SqlTable(SqlTable&& other) noexcept
    : SqlObject(std::move(other))
    , m_metadata(std::move(other.m_metadata))
    , d_ptr(std::move(other.d_ptr)) {
}

// Move assignment operator
SqlTable& SqlTable::operator=(SqlTable&& other) noexcept {
    if (this != &other) {
        SqlObject::operator=(std::move(other));
        m_metadata = std::move(other.m_metadata);
        d_ptr = std::move(other.d_ptr);
    }
    return *this;
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlTable SqlTable::fromDatabase(std::string_view name, std::shared_ptr<SqlDatabase> db) {
    SqlTable table(name, db);

    if (db) {
        // Load metadata from database
        table.loadMetadata();
    }

    return table;
}

std::vector<SqlTable> SqlTable::allTables(std::shared_ptr<SqlDatabase> db,
                                          std::string_view schema,
                                          SqlObjectType type) {
    std::vector<SqlTable> tables;

    if (!db || !db->isConnected()) {
        return tables;
    }

    try {
        SqlQuery query(*db);

        // Build query to get table information
        std::string sql = "SELECT TABLE_NAME, TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE 1=1";

        std::vector<Data> parameters;

        if (!schema.empty()) {
            sql += " AND TABLE_SCHEMA = ?";
            parameters.push_back(std::string(schema));
        }

        if (type == SqlObjectType::Table) {
            sql += " AND TABLE_TYPE = 'BASE TABLE'";
        } else if (type == SqlObjectType::View) {
            sql += " AND TABLE_TYPE = 'VIEW'";
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return tables;
        }

        while (query.next()) {
            std::string tableName = query.value("TABLE_NAME").to<std::string>();
            std::string tableSchema = query.value("TABLE_SCHEMA").to<std::string>();

            SqlTable table = SqlTable::fromDatabase(tableName, db);
            table.setSchema(tableSchema);
            tables.push_back(std::move(table));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return tables;
}

//----------------------------------------------------------------------
// Enhanced Factory Methods
//----------------------------------------------------------------------

SqlTable SqlTable::temporary(std::string_view name) {
    auto table = SqlTable(name);
    if (table.metadata()) {
        table.metadata()->isTemporary = true;
    }
    return table;
}

//----------------------------------------------------------------------
// Enhanced Table Metadata
//----------------------------------------------------------------------

SqlTable::SqlTableMetadata* SqlTable::metadata() const noexcept {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    return m_metadata.get();
}

SqlTable& SqlTable::setMetadata(const SqlTableMetadata& metadata) {
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlTableMetadata>();
    }
    *m_metadata = metadata;
    return *this;
}

bool SqlTable::hasMetadata() const noexcept {
    return m_metadata != nullptr;
}

bool SqlTable::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

bool SqlTable::loadMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query database for table metadata
        std::string sql = "SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?";

        query.setQuery(sql);
        query.prepare();
        query.bind(1, std::string(name()))
            .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to load table metadata: {}", query.lastError().message()));
            return false;
        }

        if (query.next()) {
            // Initialize metadata if not exists
            if (!m_metadata) {
                m_metadata = std::make_shared<SqlTableMetadata>();
            }

            // Extract metadata from query result
            m_metadata->schema = query.value("TABLE_SCHEMA").to<std::string>();
            m_metadata->engine = query.value("ENGINE").to<std::string>();
            m_metadata->comment = query.value("TABLE_COMMENT").to<std::string>();

            // Additional fields as available
            auto tableRows = query.value("TABLE_ROWS");
            if (!tableRows.isNull()) {
                m_metadata->rowCount = tableRows.to<size_t>();
            }

            d_ptr->clearError();
            return true;
        }

        d_ptr->setError("Table not found in database");
        return false;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception loading metadata: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Metadata Accessor Methods
//----------------------------------------------------------------------

std::string_view SqlTable::schema() const noexcept {
    return metadata()->schema;
}

SqlTable& SqlTable::setSchema(std::string_view schema) {
    metadata()->schema = schema;
    return *this;
}

std::string_view SqlTable::engine() const noexcept {
    return metadata()->engine;
}

SqlTable& SqlTable::setEngine(std::string_view engine) {
    metadata()->engine = engine;
    return *this;
}

std::string_view SqlTable::catalog() const noexcept {
    return metadata()->catalog;
}

SqlTable& SqlTable::setCatalog(std::string_view catalog) {
    metadata()->catalog = catalog;
    return *this;
}

std::string_view SqlTable::comment() const noexcept {
    return metadata()->comment;
}

SqlTable& SqlTable::setComment(std::string_view comment) {
    metadata()->comment = comment;
    return *this;
}

std::string_view SqlTable::charset() const noexcept {
    return metadata()->charset;
}

SqlTable& SqlTable::setCharset(std::string_view charset) {
    metadata()->charset = charset;
    return *this;
}

std::string_view SqlTable::collation() const noexcept {
    return metadata()->collation; // Fixed: was returning charset
}

SqlTable& SqlTable::setCollation(std::string_view collation) {
    metadata()->collation = collation;
    return *this;
}

bool SqlTable::isTemporary() const noexcept {
    return metadata()->isTemporary;
}

SqlTable& SqlTable::setTemporary(bool temporary) noexcept {
    metadata()->isTemporary = temporary;
    return *this;
}

bool SqlTable::isView() const noexcept {
    return metadata()->isView;
}

SqlTable& SqlTable::setView(bool view) noexcept {
    metadata()->isView = view;
    return *this;
}

//----------------------------------------------------------------------
// Column Management
//----------------------------------------------------------------------

std::vector<SqlColumn> SqlTable::columns() const noexcept {
    return m_metadata ? m_metadata->columns : std::vector<SqlColumn>{};
}

SqlColumn SqlTable::column(std::string_view columnName) {
    // If driver is available, try to find the column in loaded metadata
    const auto& cols = columns();
    auto it = std::find_if(cols.begin(), cols.end(),
                           [columnName](const auto& col) {
                               return col.name() == columnName;
                           });

    if (it != cols.end()) {
        return *it;
    }

    // If not found and we have a database connection, try to load from database
    if (d_ptr && d_ptr->isValid()) {
        SqlColumn col = SqlColumn::fromDatabase(columnName, d_ptr->database.get());
        col.setTable(*this);

        // Add to metadata
        addColumn(col);
        return col;
    }

    // Return a new column with this table association
    SqlColumn col(columnName, *this);
    return col;
}

SqlColumn SqlTable::column(std::string_view columnName) const {
    // If driver is available, try to find the column in loaded metadata
    const auto& cols = columns();
    auto it = std::find_if(cols.begin(), cols.end(),
                           [columnName](const auto& col) {
                               return col.name() == columnName;
                           });

    if (it != cols.end()) {
        return *it;
    }
    return SqlColumn(columnName, *this);
}

SqlTable& SqlTable::addColumn(const SqlColumn& column) {
    auto& cols = metadata()->columns;

    std::string columnName = std::string(column.name());

    // Check if column already exists
    auto it = std::find_if(cols.begin(), cols.end(),
                           [columnName](const auto& col) {
                               return col.name() == columnName;
                           });

    if (it != cols.end()) {
        // Update existing column
        *it = column;
    } else {
        // Add new column
        cols.push_back(column);
    }

    return *this;
}

SqlTable& SqlTable::addColumn(SqlColumn&& column) {
    auto& cols = metadata()->columns;

    std::string columnName = std::string(column.name());

    // Check if column already exists
    auto it = std::find_if(cols.begin(), cols.end(),
                           [columnName](const auto& col) {
                               return col.name() == columnName;
                           });

    if (it != cols.end()) {
        // Update existing column
        *it = std::move(column);
    } else {
        // Add new column
        cols.push_back(std::move(column));
    }

    return *this;
}

SqlTable& SqlTable::dropColumn(std::string_view columnName) {
    auto& cols = metadata()->columns;

    // Check if column already exists
    auto it = std::find_if(cols.begin(), cols.end(),
                           [columnName](const auto& col) {
                               return col.name() == columnName;
                           });

    if (it != cols.end()) {
        // Remove from vector
        cols.erase(it);

        // If we have a database connection, drop the column from the database
        if (d_ptr && d_ptr->isValid()) {
            try {
                SqlQuery query(*d_ptr->database);
                std::string sql = "ALTER TABLE " + qualifiedName() + " DROP COLUMN " + std::string(columnName);
                query.setQuery(sql);
                query.execute(); // Ignore errors for now
            } catch (...) {
                // Ignore database errors for metadata operations
            }
        }
    }

    return *this;
}

SqlTable& SqlTable::modifyColumn(const SqlColumn& column) {
    // Update in metadata
    addColumn(column);

    // If we have a database connection, modify the column in the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = "ALTER TABLE " + qualifiedName() + " MODIFY COLUMN " + column.definitionSql();
            query.setQuery(sql);
            query.execute(); // Ignore errors for now
        } catch (...) {
            // Ignore database errors for metadata operations
        }
    }

    return *this;
}

SqlTable& SqlTable::modifyColumn(SqlColumn&& column) {
    // Update in metadata
    addColumn(std::move(column));

    // If we have a database connection, modify the column in the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = "ALTER TABLE " + qualifiedName() + " MODIFY COLUMN " + column.definitionSql();
            query.setQuery(sql);
            query.execute(); // Ignore errors for now
        } catch (...) {
            // Ignore database errors for metadata operations
        }
    }

    return *this;
}

bool SqlTable::hasColumn(std::string_view columnName) const noexcept {
    if (!m_metadata) {
        return false;
    }

    const auto& cols = columns();
    return std::ranges::any_of(cols, [columnName](const auto& col) {
        return col.name() == columnName;
    });
}

size_t SqlTable::columnCount() const noexcept {
    return metadata()->columns.size();
}

std::vector<SqlColumn> SqlTable::primaryKeyColumns() const {
    std::vector<SqlColumn> result;
    const auto& cols = columns();

    std::copy_if(cols.begin(), cols.end(), std::back_inserter(result),
                 [](const SqlColumn& col) {
                     return col.isPrimaryKey();
                 });

    return result;
}

std::vector<SqlColumn> SqlTable::foreignKeyColumns() const {
    std::vector<SqlColumn> result;
    const auto& cols = columns();

    std::copy_if(cols.begin(), cols.end(), std::back_inserter(result),
                 [](const SqlColumn& col) {
                     return col.hasConstraint(SqlColumnConstraint::ForeignKey);
                 });

    return result;
}

std::vector<SqlColumn> SqlTable::uniqueColumns() const {
    std::vector<SqlColumn> result;
    const auto& cols = columns();

    std::copy_if(cols.begin(), cols.end(), std::back_inserter(result),
                 [](const SqlColumn& col) {
                     return col.isUnique();
                 });

    return result;
}

//----------------------------------------------------------------------
// Index Management
//----------------------------------------------------------------------

std::vector<SqlIndex> SqlTable::indexes() const {
    return m_metadata ? m_metadata->indexes : std::vector<SqlIndex>{};
}

SqlIndex SqlTable::index(std::string_view indexName) const {
    // Search for index in metadata
    auto& indexes = metadata()->indexes;
    auto it = std::find_if(indexes.begin(), indexes.end(), [indexName](const SqlIndex& idx) {
        return idx.name() == indexName;
    });

    if (it != indexes.end()) {
        return *it;
    }

    // Return empty index if not found
    return SqlIndex{};
}

bool SqlTable::hasIndex(std::string_view indexName) const {
    if (!m_metadata) {
        return false;
    }
    auto& indexes = m_metadata->indexes;
    return std::any_of(indexes.begin(), indexes.end(),
                       [indexName](const SqlIndex& idx) {
                           return idx.name() == indexName;
                       });
}

bool SqlTable::addIndex(const SqlIndex& index) {
    // Add to metadata
    auto& indexes = metadata()->indexes;

    // Check if index already exists
    auto it = std::find_if(indexes.begin(), indexes.end(),
                           [&index](const SqlIndex& idx) {
                               return idx.name() == index.name();
                           });

    if (it != indexes.end()) {
        // Update existing index
        *it = index;
    } else {
        // Add new index
        indexes.push_back(index);
    }

    // If we have a database connection, create the index in the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = index.createSql();
            query.setQuery(sql);
            return query.execute();
        } catch (...) {
            return false;
        }
    }

    return true;
}

bool SqlTable::addIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique) {
    // Create SqlIndex object
    SqlIndex index(indexName);
    index.setTableName(std::string(name()));
    index.setColumns(columnNames);
    index.setIndexType(unique ? SqlIndexType::Unique : SqlIndexType::Normal);

    return addIndex(index);
}

bool SqlTable::dropIndex(std::string_view indexName) {
    // Remove from metadata
    auto& indexes = metadata()->indexes;
    auto it = std::find_if(indexes.begin(), indexes.end(),
                           [indexName](const SqlIndex& idx) {
                               return idx.name() == indexName;
                           });

    if (it != indexes.end()) {
        indexes.erase(it);
    }

    // If we have a database connection, drop the index from the database
    if (d_ptr && d_ptr->isValid()) {
        try {
            SqlQuery query(*d_ptr->database);
            std::string sql = "DROP INDEX " + std::string(indexName) + " ON " + qualifiedName();
            query.setQuery(sql);
            return query.execute();
        } catch (...) {
            return false;
        }
    }

    return true;
}

//----------------------------------------------------------------------
// Row Management
//----------------------------------------------------------------------

size_t SqlTable::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        // Return cached value if available
        if (metadata()->rowCount.has_value()) {
            return metadata()->rowCount.value();
        }
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT COUNT(*) FROM " + qualifiedName();
        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}


std::vector<SqlRow> SqlTable::selectAll() const {
    std::vector<SqlRow> rows;

    if (!d_ptr || !d_ptr->isValid()) {
        return rows;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT * FROM " + qualifiedName();
        query.setQuery(sql);

        if (!query.execute()) {
            return rows;
        }

        while (query.next()) {
            auto record = query.record();
            SqlRow row(std::string(this->name()));

            // Convert SqlRecord to SqlRow
            auto fieldNames = record.fieldNames();
            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            rows.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return rows;
}

std::vector<SqlRow> SqlTable::select(const std::string& whereClause, const std::vector<Data>& parameters) const {
    std::vector<SqlRow> rows;

    if (!d_ptr || !d_ptr->isValid()) {
        return rows;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT * FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return rows;
        }

        while (query.next()) {
            auto record = query.record();
            SqlRow row(std::string(this->name()));

            // Convert SqlRecord to SqlRow
            auto fieldNames = record.fieldNames();
            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            rows.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return rows;
}

bool SqlTable::insert(const SqlRow& row) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build INSERT statement
        auto fieldNames = row.columnNames();
        if (fieldNames.empty()) {
            d_ptr->setError("Row has no fields");
            return false;
        }

        std::string sql = "INSERT INTO " + qualifiedName() + " (";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += fieldNames[i];
        }
        sql += ") VALUES (";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += "?";
        }
        sql += ")";

        query.setQuery(sql).prepare();

        // Bind values
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            query.bind(static_cast<int>(i + 1), row.value(fieldNames[i]));
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to insert row: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception inserting row: " + std::string(e.what()));
        return false;
    }
}

size_t SqlTable::insert(const std::vector<SqlRow>& rows) {
    if (rows.empty()) {
        return 0;
    }

    size_t insertedCount = 0;

    // For now, insert rows one by one
    // TODO: Implement batch insert for better performance
    for (const auto& row : rows) {
        if (insert(row)) {
            ++insertedCount;
        }
    }

    return insertedCount;
}

size_t SqlTable::update(const SqlRow& row, const std::string& whereClause, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build UPDATE statement
        auto fieldNames = row.columnNames();
        if (fieldNames.empty()) {
            d_ptr->setError("Row has no fields");
            return 0;
        }

        std::string sql = "UPDATE " + qualifiedName() + " SET ";
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            if (i > 0) sql += ", ";
            sql += fieldNames[i] + " = ?";
        }

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind field values
        for (size_t i = 0; i < fieldNames.size(); ++i) {
            query.bind(static_cast<int>(i + 1), row.value(fieldNames[i]));
        }

        // Bind WHERE clause parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(fieldNames.size() + i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to update rows: {}", query.lastError().message()));
            return 0;
        }

        d_ptr->clearError();
        return static_cast<size_t>(query.numRowsAffected());

    } catch (const std::exception& e) {
        d_ptr->setError("Exception updating rows: " + std::string(e.what()));
        return 0;
    }
}

size_t SqlTable::deleteRows(const std::string& whereClause, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DELETE statement
        std::string sql = "DELETE FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to delete rows: {}", query.lastError().message()));
            return 0;
        }

        d_ptr->clearError();
        return static_cast<size_t>(query.numRowsAffected());

    } catch (const std::exception& e) {
        d_ptr->setError("Exception deleting rows: " + std::string(e.what()));
        return 0;
    }
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

std::shared_ptr<SqlDatabase> SqlTable::database() const {
    return d_ptr ? d_ptr->database : nullptr;
}

void SqlTable::setDatabase(std::shared_ptr<SqlDatabase> database) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlTablePrivate>();
    }
    d_ptr->setDatabase(database);
}

bool SqlTable::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if table exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ?";

        query.setQuery(sql);
        query.prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlTable::create(const std::vector<SqlColumn>& columns, bool ifNotExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE TABLE statement
        std::string sql = createSql(ifNotExists);

        // If columns are provided, use them; otherwise use metadata columns
        if (!columns.empty()) {
            // Update metadata with provided columns
            metadata()->columns = columns;
        }

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create table: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP TABLE statement
        std::string sql = dropSql(ifExists);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop table: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::truncate() {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build TRUNCATE TABLE statement
        std::string sql = "TRUNCATE TABLE " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to truncate table: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception truncating table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::rename(std::string_view newName) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build RENAME TABLE statement
        std::string sql = "RENAME TABLE " + qualifiedName() + " TO " + std::string(newName);

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to rename table: {}", query.lastError().message()));
            return false;
        }

        // Update the name in the object
        setName(newName);

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception renaming table: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::execute(const std::string& sql, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        d_ptr->setError("No database connection available");
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to execute SQL: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception executing SQL: " + std::string(e.what()));
        return false;
    }
}

bool SqlTable::refreshMetadata() {
    // Clear existing metadata and reload
    if (m_metadata) {
        m_metadata.reset();
    }
    return loadMetadata();
}

bool SqlTable::isEmpty() const {
    return rowCount() == 0;
}

size_t SqlTable::estimatedRowCount() const {
    if (metadata()->rowCount.has_value()) {
        return metadata()->rowCount.value();
    }
    return rowCount(); // Fall back to actual count
}

size_t SqlTable::actualRowCount() const {
    return rowCount();
}

size_t SqlTable::dataSize() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT data_length FROM information_schema.tables WHERE table_name = ? AND table_schema = ?";
        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlTable::indexSize() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);
        std::string sql = "SELECT index_length FROM information_schema.tables WHERE table_name = ? AND table_schema = ?";
        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()))
             .bind(2, metadata()->schema.empty() ? "public" : metadata()->schema);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

//----------------------------------------------------------------------
// SqlObject Implementation
//----------------------------------------------------------------------

std::string SqlTable::qualifiedName() const {
    if (name().empty()) {
        return {};
    }

    std::string tableName = std::string(name());

    // Add schema prefix if present
    if (!schema().empty()) {
        tableName = std::format("{}.{}", schema(), tableName);
    }

    // Add alias if present
    // if (alias().empty()) {
    //     return std::format("{} AS {}", tableName, alias());
    // }

    return tableName;
}

std::string SqlTable::toSql() const {
    return qualifiedName();
}

std::string SqlTable::createSql(bool ifNotExists) const {
    std::ostringstream sql;

    // CREATE TABLE clause
    sql << "CREATE ";
    if (metadata()->isTemporary) {
        sql << "TEMPORARY ";
    }
    sql << "TABLE ";

    if (ifNotExists) {
        sql << "IF NOT EXISTS ";
    }

    sql << qualifiedName() << " (\n";

    // Add columns
    auto& columns = metadata()->columns;
    if (columns.empty()) {
        // If no columns in metadata, create a minimal table
        sql << "id INTEGER PRIMARY KEY";
    } else {
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) sql << ",\n";
            sql << "    " << columns[i].definitionSql();
        }
    }

    sql << "\n)";

    // Add table options
    if (!metadata()->engine.empty()) {
        sql << " ENGINE=" << metadata()->engine;
    }
    if (!metadata()->charset.empty()) {
        sql << " DEFAULT CHARSET=" << metadata()->charset;
    }
    if (!metadata()->collation.empty()) {
        sql << " COLLATE=" << metadata()->collation;
    }
    if (!metadata()->comment.empty()) {
        sql << " COMMENT='" << metadata()->comment << "'";
    }

    return sql.str();
}

std::string SqlTable::alterSql() const {
    // TODO: Implement ALTER TABLE statement generation
    // This would require tracking changes to the table structure
    return "ALTER TABLE " + qualifiedName() + " /* TODO: implement alter logic */";
}

std::string SqlTable::dropSql(bool ifExists) const {
    std::string sql = "DROP TABLE ";
    if (ifExists) {
        sql += "IF EXISTS ";
    }
    sql += qualifiedName();
    return sql;
}

std::string SqlTable::truncateSql() const {
    return "TRUNCATE TABLE " + qualifiedName();
}

std::string SqlTable::renameSql(std::string_view newName) const {
    return "RENAME TABLE " + qualifiedName() + " TO " + std::string(newName);
}

} // namespace database
