#ifndef DATABASE_SQL_ROW_H
#define DATABASE_SQL_ROW_H

#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <chrono>
#include <optional>

#include "sql_column.h"

namespace database {

// Forward declarations
class SqlRowPrivate;

/**
 * @brief Class representing a database row
 *
 * This class encapsulates a database row with column values.
 * It provides methods for setting and getting column values.
 */
class SqlRow final : public SqlObject {
public:
    /**
     * @brief Metadata for SQL row objects (Tier 2 - Extended Metadata)
     *
     * This struct contains comprehensive descriptive information about table rows.
     */
    struct SqlRowMetadata {
        std::string tableName;                              ///< Parent table name
        std::vector<std::string> columnNames;               ///< Column names in order
        std::optional<std::chrono::system_clock::time_point> insertTime; ///< Insert timestamp
        std::optional<std::chrono::system_clock::time_point> updateTime; ///< Last update timestamp
        std::optional<std::string> version;                 ///< Row version for optimistic locking
        std::optional<size_t> rowId;                        ///< Physical row identifier
        bool isDeleted = false;                             ///< Soft delete flag
        bool isDirty = false;                               ///< Has unsaved changes
        std::unordered_map<std::string, std::string> properties; ///< Additional properties

    /**
     * @brief Default constructor
     */
        SqlRowMetadata() = default;

    /**
     * @brief Constructor with basic information
     */
        SqlRowMetadata(std::string tableName, std::vector<std::string> columnNames)
            : tableName(std::move(tableName)), columnNames(std::move(columnNames)) {}
    };

    SqlRow() = default;
    explicit SqlRow(std::string_view tableName);
    explicit SqlRow(std::string_view tableName, DataMap values);
    explicit SqlRow(std::string_view tableName, DataMap&& values) noexcept;
    explicit SqlRow(DataMap values);
    explicit SqlRow(DataMap&& values) noexcept;

    // Copy/move operations
    SqlRow(const SqlRow& other);
    SqlRow& operator=(const SqlRow& other);
    SqlRow(SqlRow&& other) noexcept;
    SqlRow& operator=(SqlRow&& other) noexcept;

    //----------------------------------------------------------------------
    // Static Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlRow fromDatabase(std::shared_ptr<SqlTable> table,
                                             std::string_view whereClause,
                                             bool loadMetadata = true);

    //----------------------------------------------------------------------
    // Row Metadata
    //----------------------------------------------------------------------
    [[nodiscard]] std::shared_ptr<SqlRowMetadata> metadata() const;
    void setMetadata(const SqlRowMetadata& metadata);

    [[nodiscard]] Data value(std::string_view columnName) const;
    [[nodiscard]] Data value(size_t index) const;
    void setValue(std::string_view columnName, Data value);
    void setValue(size_t index, Data value);
    [[nodiscard]] const DataMap& values() const;
    void setValues(DataMap values);

    [[nodiscard]] bool hasColumn(std::string_view columnName) const;
    [[nodiscard]] size_t columnCount() const;

    [[nodiscard]] std::vector<std::string> columnNames() const;
    void removeColumn(std::string_view columnName);

    [[nodiscard]] bool isEmpty() const;
    void clear();

    [[nodiscard]] bool isDirty() const;
    void setDirty(bool dirty);
    [[nodiscard]] bool isDeleted() const;
    void setDeleted(bool deleted);
    [[nodiscard]] std::optional<size_t> rowId() const;
    void setRowId(std::optional<size_t> rowId);
    [[nodiscard]] std::optional<std::string> version() const;
    void setVersion(std::optional<std::string> version);


    [[nodiscard]] std::string_view tableName() const;
    void setTableName(std::string tableName);

    Data& operator[](std::string_view columnName);
    const Data& operator[](std::string_view columnName) const;
    Data& operator[](size_t index);
    const Data& operator[](size_t index) const;

    //----------------------------------------------------------------------
    // Table Association
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTable table() const noexcept;
    void setTable(const SqlTable& table) noexcept;

    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const;
    bool save();
    bool insert();
    bool update(std::string_view whereClause = {});
    bool deleteFromDatabase(std::string_view whereClause = {});
    bool reload(std::string_view whereClause = {});
    [[nodiscard]] bool exists(std::string_view whereClause = {}) const;
    bool refreshMetadata();
    [[nodiscard]] bool isValid() const;
    [[nodiscard]] std::vector<std::string> validationErrors() const;
    [[nodiscard]] bool isColumnValid(std::string_view columnName) const;

    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;
    [[nodiscard]] std::string toJson() const;

    [[nodiscard]] std::string toInsertStatement(std::string_view tableName = {}) const;
    [[nodiscard]] std::string toUpdateStatement(std::string_view whereClause,
                                                std::string_view tableName = {}) const;

private:
    DataMap m_values;
    mutable std::shared_ptr<SqlRowMetadata> m_metadata;
    mutable std::shared_ptr<SqlRowPrivate> d_ptr;

    // Helper methods for tier management
    void ensureDatabase() const;

    // Internal helpers
    void updateColumnNames();
    Data& getValueRef(std::string_view columnName);
    const Data& getValueRef(std::string_view columnName) const;
    Data& getValueRef(size_t index);
    const Data& getValueRef(size_t index) const;
    std::string buildDefaultWhereClause() const;
};

} // namespace database

#endif // DATABASE_SQL_ROW_H
