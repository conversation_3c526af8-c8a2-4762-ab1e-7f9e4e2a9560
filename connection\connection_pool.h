#ifndef DATABASE_CONNECTION_POOL_H
#define DATABASE_CONNECTION_POOL_H

#include "../sql_database.h"
#include <memory>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <functional>
#include <unordered_map>
#include <string>

namespace database {

/**
 * @brief Connection pool for database connections
 *
 * This class provides a pool of database connections that can be reused.
 * It helps improve performance by avoiding the overhead of creating new
 * connections for each request.
 */
class ConnectionPool {
public:
    /**
     * @brief Constructor
     * @param driverName The driver name
     * @param params The connection parameters
     * @param initialSize The initial pool size
     * @param maxSize The maximum pool size
     * @param timeout The connection timeout in milliseconds
     */
    ConnectionPool(
        const std::string& driverName,
        const ConnectionParams& params,
        size_t initialSize = 5,
        size_t maxSize = 10,
        std::chrono::milliseconds timeout = std::chrono::seconds(30));

    /**
     * @brief Destructor
     */
    ~ConnectionPool();

    /**
     * @brief Get a connection from the pool
     * @return A shared pointer to a database connection
     */
    std::shared_ptr<SqlDatabase> getConnection();

    /**
     * @brief Release a connection back to the pool
     * @param connection The connection to release
     */
    void releaseConnection(std::shared_ptr<SqlDatabase> connection);

    /**
     * @brief Get the number of active connections
     * @return The number of active connections
     */
    size_t getActiveConnectionCount() const;

    /**
     * @brief Get the number of idle connections
     * @return The number of idle connections
     */
    size_t getIdleConnectionCount() const;

    /**
     * @brief Get the total number of connections
     * @return The total number of connections
     */
    size_t getTotalConnectionCount() const;

    /**
     * @brief Set the connection validation function
     * @param validator The validation function
     */
    void setConnectionValidator(std::function<bool(std::shared_ptr<SqlDatabase>)> validator);

    /**
     * @brief Set the connection initialization function
     * @param initializer The initialization function
     */
    void setConnectionInitializer(std::function<bool(std::shared_ptr<SqlDatabase>)> initializer);

    /**
     * @brief Close all connections in the pool
     */
    void close();

private:
    /**
     * @brief Create a new connection
     * @return A shared pointer to a database connection
     */
    std::shared_ptr<SqlDatabase> createConnection();

    /**
     * @brief Validate a connection
     * @param connection The connection to validate
     * @return True if valid, false otherwise
     */
    bool validateConnection(std::shared_ptr<SqlDatabase> connection);

    /**
     * @brief Initialize a connection
     * @param connection The connection to initialize
     * @return True if successful, false otherwise
     */
    bool initializeConnection(std::shared_ptr<SqlDatabase> connection);

    std::string m_driverName;
    ConnectionParams m_params;
    size_t m_initialSize;
    size_t m_maxSize;
    std::chrono::milliseconds m_timeout;
    
    std::queue<std::shared_ptr<SqlDatabase>> m_idleConnections;
    std::unordered_map<SqlDatabase*, std::shared_ptr<SqlDatabase>> m_activeConnections;
    
    std::function<bool(std::shared_ptr<SqlDatabase>)> m_validator;
    std::function<bool(std::shared_ptr<SqlDatabase>)> m_initializer;
    
    mutable std::mutex m_mutex;
    std::condition_variable m_condition;
    bool m_closed;
};

} // namespace database

#endif // DATABASE_CONNECTION_POOL_H
