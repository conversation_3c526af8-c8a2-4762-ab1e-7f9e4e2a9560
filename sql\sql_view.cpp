#include "sql_view.h"

#include <sstream>

#include "sql_database.h"
#include "sql_query.h"
#include "sql_column.h"
#include "sql_row.h"
#include "exception/sql_error.h"

namespace database {

/**
 * @brief Private implementation class for SqlView
 *
 * This class provides the database interaction layer (Tier 3) for SqlView.
 * It manages the actual database operations and maintains connection state.
 */
class SqlViewPrivate {
public:
    SqlViewPrivate() = default;
    ~SqlViewPrivate() = default;

    // Database connection
    std::shared_ptr<SqlDatabase> database;

    // Error information
    SqlError lastError;

    // State flags
    bool databaseEnabled = false;

    void setDatabase(std::shared_ptr<SqlDatabase> db) {
        database = db;
        databaseEnabled = (db != nullptr);
    }

    bool isValid() const {
        return databaseEnabled && database && database->isOpen();
    }

    void setError(const std::string& message, ErrorCode code = ErrorCode::Unknown) {
        lastError = SqlError(message, code);
    }

    void clearError() {
        lastError.clear();
    }
};

//----------------------------------------------------------------------
// Constructors and Destructors
//----------------------------------------------------------------------

SqlView::SqlView()
    : SqlObject("", SqlObjectType::View) {
}

SqlView::SqlView(std::string name)
    : SqlObject(std::move(name), SqlObjectType::View) {
}

SqlView::SqlView(std::string name, std::string definition)
    : SqlObject(std::move(name), SqlObjectType::View) {
    // Initialize metadata with definition
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlViewMetadata>();
    }
    m_metadata->definition = std::move(definition);
}

SqlView::SqlView(std::string name, SqlViewMetadata metadata)
    : SqlObject(std::move(name), SqlObjectType::View) {
    m_metadata = std::make_shared<SqlViewMetadata>(std::move(metadata));
}

SqlView::SqlView(std::string name, SqlViewMetadata metadata, std::shared_ptr<SqlDatabase> database)
    : SqlObject(std::move(name), SqlObjectType::View) {
    m_metadata = std::make_shared<SqlViewMetadata>(std::move(metadata));

    // Set up database connection
    if (database) {
        if (!d_ptr) {
            d_ptr = std::make_shared<SqlViewPrivate>();
        }
        d_ptr->setDatabase(database);
    }
}

SqlView::~SqlView() = default;

// Copy constructor
SqlView::SqlView(const SqlView& other)
    : SqlObject(other), m_metadata(other.m_metadata), d_ptr(other.d_ptr) {
}

// Copy assignment operator
SqlView& SqlView::operator=(const SqlView& other) {
    if (this != &other) {
        SqlObject::operator=(other);
        m_metadata = other.m_metadata;
        d_ptr = other.d_ptr;
    }
    return *this;
}

// Move constructor
SqlView::SqlView(SqlView&& other) noexcept
    : SqlObject(std::move(other)), m_metadata(std::move(other.m_metadata)),
    d_ptr(std::move(other.d_ptr)) {
}

// Move assignment operator
SqlView& SqlView::operator=(SqlView&& other) noexcept {
    if (this != &other) {
        SqlObject::operator=(std::move(other));
        m_metadata = std::move(other.m_metadata);
        d_ptr = std::move(other.d_ptr);
    }
    return *this;
}

//----------------------------------------------------------------------
// Extended Metadata Operations
//----------------------------------------------------------------------

bool SqlView::hasMetadata() const noexcept {
    return m_metadata != nullptr;
}

std::shared_ptr<SqlView::SqlViewMetadata> SqlView::metadata() const {
    // Lazy initialization of metadata
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlViewMetadata>();
    }
    return m_metadata;
}

void SqlView::setMetadata(SqlViewMetadata metadata) {
    m_metadata = std::make_shared<SqlViewMetadata>(std::move(metadata));
}

void SqlView::setMetadata(std::shared_ptr<SqlViewMetadata> metadata) {
    m_metadata = std::move(metadata);
}

std::string SqlView::schema() const {
    return hasMetadata() ? m_metadata->schema : std::string{};
}

void SqlView::setSchema(std::string schema) {
    metadata()->schema = std::move(schema);
}

std::string SqlView::definition() const {
    return hasMetadata() ? m_metadata->definition : std::string{};
}

void SqlView::setDefinition(std::string definition) {
    metadata()->definition = std::move(definition);
}

std::string SqlView::comment() const {
    return hasMetadata() ? m_metadata->comment : std::string{};
}

void SqlView::setComment(std::string comment) {
    metadata()->comment = std::move(comment);
}

std::vector<std::string> SqlView::referencedTables() const {
    return hasMetadata() ? m_metadata->referencedTables : std::vector<std::string>{};
}

void SqlView::setReferencedTables(std::vector<std::string> tables) {
    metadata()->referencedTables = std::move(tables);
}

std::vector<std::string> SqlView::columnNames() const {
    return hasMetadata() ? m_metadata->columns : std::vector<std::string>{};
}

void SqlView::setColumnNames(std::vector<std::string> columns) {
    metadata()->columns = std::move(columns);
}

bool SqlView::isUpdatable() const {
    return hasMetadata() ? m_metadata->isUpdatable : false;
}

void SqlView::setUpdatable(bool updatable) {
    metadata()->isUpdatable = updatable;
}

bool SqlView::isMaterialized() const {
    return hasMetadata() ? m_metadata->isMaterialized : false;
}

void SqlView::setMaterialized(bool materialized) {
    metadata()->isMaterialized = materialized;
}

bool SqlView::hasCheckOption() const {
    return hasMetadata() ? m_metadata->withCheckOption : false;
}

void SqlView::setCheckOption(bool withCheckOption) {
    metadata()->withCheckOption = withCheckOption;
}

std::string SqlView::checkOptionType() const {
    return hasMetadata() ? m_metadata->checkOption : std::string{};
}

void SqlView::setCheckOptionType(std::string checkOption) {
    metadata()->checkOption = std::move(checkOption);
    metadata()->withCheckOption = !m_metadata->checkOption.empty();
}

std::string SqlView::securityType() const {
    return hasMetadata() ? m_metadata->securityType : std::string{};
}

void SqlView::setSecurityType(std::string securityType) {
    metadata()->securityType = std::move(securityType);
}

std::string SqlView::definer() const {
    return hasMetadata() ? m_metadata->definer : std::string{};
}

void SqlView::setDefiner(std::string definer) {
    metadata()->definer = std::move(definer);
}

//----------------------------------------------------------------------
// Database Interaction Operations
//----------------------------------------------------------------------

bool SqlView::isConnected() const noexcept {
    return d_ptr && d_ptr->isValid();
}

std::shared_ptr<SqlDatabase> SqlView::database() const {
    return d_ptr ? d_ptr->database : nullptr;
}

void SqlView::setDatabase(std::shared_ptr<SqlDatabase> database) {
    if (!d_ptr) {
        d_ptr = std::make_shared<SqlViewPrivate>();
    }
    d_ptr->database = std::move(database);
}

bool SqlView::exists() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }
    
    try {
        SqlQuery query(*d_ptr->database);

        // Query to check if view exists
        std::string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        return query.value(0).to<int>() > 0;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::create(bool orReplace) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (definition().empty()) {
        if (d_ptr) {
            d_ptr->setError("View definition is empty");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build CREATE VIEW statement
        std::string sql = createStatement();
        if (orReplace) {
            // Replace CREATE with CREATE OR REPLACE
            size_t pos = sql.find("CREATE VIEW");
            if (pos != std::string::npos) {
                sql.replace(pos, 11, "CREATE OR REPLACE VIEW");
            }
        }

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to create view: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->createdTime = std::chrono::system_clock::now();
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception creating view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::drop(bool ifExists) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DROP VIEW statement
        std::ostringstream sql;
        sql << "DROP VIEW ";
        if (ifExists) {
            sql << "IF EXISTS ";
        }
        sql << qualifiedName();

        query.setQuery(sql.str());

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to drop view: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception dropping view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::rename(const std::string& newName) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build RENAME VIEW statement (database-specific)
        std::string sql = "ALTER VIEW " + qualifiedName() + " RENAME TO " + newName;

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to rename view: {}", query.lastError().message()));
            return false;
        }

        // Update object name
        setName(newName);

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception renaming view: " + std::string(e.what()));
        return false;
    }
}

bool SqlView::refresh() {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (!isMaterialized()) {
        if (d_ptr) {
            d_ptr->setError("View is not materialized");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build REFRESH MATERIALIZED VIEW statement
        std::string sql = "REFRESH MATERIALIZED VIEW " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to refresh materialized view: {}", query.lastError().message()));
            return false;
        }

        // Update metadata
        if (m_metadata) {
            m_metadata->lastRefreshTime = std::chrono::system_clock::now();
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception refreshing materialized view: " + std::string(e.what()));
        return false;
    }
}

//----------------------------------------------------------------------
// Data Access Methods
//----------------------------------------------------------------------

std::vector<SqlColumn> SqlView::columns() const {
    std::vector<SqlColumn> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get column information
        std::string sql = "SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT "
                          "FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }
        sql += " ORDER BY ORDINAL_POSITION";

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlColumn column(query.value("COLUMN_NAME").to<std::string>());

            // Set column metadata from query results
            auto columnMetadata = std::make_shared<SqlColumn::SqlColumnMetadata>();
            columnMetadata->tableName = std::string(name());
            columnMetadata->dataType = stringToSqlDataType(query.value("DATA_TYPE").to<std::string>());
            columnMetadata->isNullable = (query.value("IS_NULLABLE").to<std::string>() == "YES");

            auto defaultValue = query.value("COLUMN_DEFAULT");
            if (!defaultValue.isNull()) {
                columnMetadata->defaultValue = defaultValue;
            }

            column.setMetadata(*columnMetadata);
            result.push_back(std::move(column));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

SqlColumn SqlView::column(const std::string& columnName) const {
    auto allColumns = columns();

    for (const auto& col : allColumns) {
        if (col.name() == columnName) {
            return col;
        }
    }

    // Return invalid column if not found
    return SqlColumn{};
}

size_t SqlView::rowCount() const {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build COUNT query
        std::string sql = "SELECT COUNT(*) FROM " + qualifiedName();

        query.setQuery(sql);

        if (!query.execute() || !query.next()) {
            return 0;
        }

        return query.value(0).to<size_t>();

    } catch (const std::exception&) {
        return 0;
    }
}

std::vector<SqlRow> SqlView::selectAll() const {
    return select("", {});
}

std::vector<SqlRow> SqlView::select(const std::string& whereClause,
                                    const std::vector<Data>& parameters) const {
    std::vector<SqlRow> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT statement
        std::string sql = "SELECT * FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlRow row(name());

            auto record = query.record();
            auto fieldNames = record.fieldNames();

            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            result.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

std::vector<SqlRow> SqlView::select(size_t limit, size_t offset) const {
    std::vector<SqlRow> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build SELECT statement with LIMIT and OFFSET
        std::string sql = "SELECT * FROM " + qualifiedName() +
                          " LIMIT " + std::to_string(limit) +
                          " OFFSET " + std::to_string(offset);

        query.setQuery(sql);

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            SqlRow row(name());

            auto record = query.record();
            auto fieldNames = record.fieldNames();

            for (const auto& fieldName : fieldNames) {
                row.setValue(fieldName, record.value(fieldName));
            }

            result.push_back(std::move(row));
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

//----------------------------------------------------------------------
// Update/Delete Operations (for updatable views)
//----------------------------------------------------------------------

bool SqlView::insert(const SqlRow& row) {
    if (!d_ptr || !d_ptr->isValid()) {
        if (d_ptr) {
            d_ptr->setError("No database connection available");
        }
        return false;
    }

    if (!isUpdatable()) {
        if (d_ptr) {
            d_ptr->setError("View is not updatable");
        }
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build INSERT statement
        std::string sql = row.toInsertStatement(qualifiedName());

        query.setQuery(sql).prepare();

        // Bind values
        size_t paramIndex = 1;
        for (const auto& pair : row.values()) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        if (!query.execute()) {
            d_ptr->setError(std::format("Failed to insert into view: {}", query.lastError().message()));
            return false;
        }

        d_ptr->clearError();
        return true;

    } catch (const std::exception& e) {
        d_ptr->setError("Exception inserting into view: " + std::string(e.what()));
        return false;
    }
}

size_t SqlView::update(const SqlRow& row, const std::string& whereClause,
                       const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    if (!isUpdatable()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build UPDATE statement
        std::string sql = row.toUpdateStatement(whereClause, qualifiedName());

        query.setQuery(sql).prepare();

        // Bind field values
        size_t paramIndex = 1;
        for (const auto& pair : row.values()) {
            query.bind(static_cast<int>(paramIndex++), pair.second);
        }

        // Bind WHERE clause parameters
        for (const auto& param : parameters) {
            query.bind(static_cast<int>(paramIndex++), param);
        }

        if (!query.execute()) {
            return 0;
        }

        return query.affectedRows();

    } catch (const std::exception&) {
        return 0;
    }
}

size_t SqlView::deleteRows(const std::string& whereClause,
                           const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return 0;
    }

    if (!isUpdatable()) {
        return 0;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Build DELETE statement
        std::string sql = "DELETE FROM " + qualifiedName();

        if (!whereClause.empty()) {
            sql += " WHERE " + whereClause;
        }

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        if (!query.execute()) {
            return 0;
        }

        return query.affectedRows();

    } catch (const std::exception&) {
        return 0;
    }
}

//----------------------------------------------------------------------
// Analysis and Utility Methods
//----------------------------------------------------------------------

std::vector<std::string> SqlView::dependencies() const {
    std::vector<std::string> result;

    if (!d_ptr || !d_ptr->isValid()) {
        return result;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get view dependencies (database-specific)
        std::string sql = "SELECT REFERENCED_TABLE_NAME FROM INFORMATION_SCHEMA.VIEW_TABLE_USAGE WHERE VIEW_NAME = ?";

        if (!schema().empty()) {
            sql += " AND VIEW_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute()) {
            return result;
        }

        while (query.next()) {
            std::string tableName = query.value("REFERENCED_TABLE_NAME").to<std::string>();
            if (!tableName.empty()) {
                result.push_back(tableName);
            }
        }

    } catch (const std::exception&) {
        // Return empty vector on error
    }

    return result;
}

bool SqlView::isDefinitionValid() const {
    if (definition().empty()) {
        return false;
    }

    if (!d_ptr || !d_ptr->isValid()) {
        return true; // Can't validate without database connection
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Try to execute EXPLAIN on the view definition
        std::string sql = "EXPLAIN " + definition();

        query.setQuery(sql);

        return query.execute();

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::refreshMetadata() {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        // Query to get view metadata
        std::string sql = "SELECT VIEW_DEFINITION, IS_UPDATABLE, CHECK_OPTION, DEFINER, SECURITY_TYPE "
                          "FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = ?";

        if (!schema().empty()) {
            sql += " AND TABLE_SCHEMA = ?";
        }

        query.setQuery(sql).prepare();
        query.bind(1, std::string(name()));

        if (!schema().empty()) {
            query.bind(2, schema());
        }

        if (!query.execute() || !query.next()) {
            return false;
        }

        // Update metadata from query results
        if (!m_metadata) {
            m_metadata = std::make_shared<SqlViewMetadata>();
        }

        m_metadata->definition = query.value("VIEW_DEFINITION").to<std::string>();
        m_metadata->isUpdatable = (query.value("IS_UPDATABLE").to<std::string>() == "YES");
        m_metadata->checkOption = query.value("CHECK_OPTION").to<std::string>();
        m_metadata->withCheckOption = !m_metadata->checkOption.empty();
        m_metadata->definer = query.value("DEFINER").to<std::string>();
        m_metadata->securityType = query.value("SECURITY_TYPE").to<std::string>();

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

bool SqlView::execute(const std::string& sql, const std::vector<Data>& parameters) {
    if (!d_ptr || !d_ptr->isValid()) {
        return false;
    }

    try {
        SqlQuery query(*d_ptr->database);

        query.setQuery(sql).prepare();

        // Bind parameters
        for (size_t i = 0; i < parameters.size(); ++i) {
            query.bind(static_cast<int>(i + 1), parameters[i]);
        }

        return query.execute();

    } catch (const std::exception&) {
        return false;
    }
}

//----------------------------------------------------------------------
// SQL Generation Methods
//----------------------------------------------------------------------

std::string SqlView::qualifiedName() const {
    if (hasMetadata() && !m_metadata->schema.empty()) {
        return m_metadata->schema + "." + std::string(name());
    }
    return std::string(name());
}

std::string SqlView::toSql() const {
    return createStatement();
}

std::string SqlView::createStatement() const {
    if (!hasMetadata() || m_metadata->definition.empty()) {
        return "";
    }
    
    std::ostringstream sql;
    
    // Start with CREATE statement
    if (m_metadata->isMaterialized) {
        sql << "CREATE MATERIALIZED VIEW ";
    } else {
        sql << "CREATE VIEW ";
    }
    
    sql << qualifiedName();
    
    // Add column list if specified
    if (!m_metadata->columns.empty()) {
        sql << " (";
        for (size_t i = 0; i < m_metadata->columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << m_metadata->columns[i];
        }
        sql << ")";
    }
    
    sql << " AS " << m_metadata->definition;
    
    // Add WITH CHECK OPTION if specified
    if (m_metadata->withCheckOption) {
        sql << " WITH ";
        if (!m_metadata->checkOption.empty()) {
            sql << m_metadata->checkOption << " ";
        }
        sql << "CHECK OPTION";
    }
    
    return sql.str();
}

std::string SqlView::dropStatement() const {
    return "DROP VIEW " + qualifiedName();
}

//----------------------------------------------------------------------
// Static Factory Methods
//----------------------------------------------------------------------

SqlView SqlView::fromDatabase(std::shared_ptr<SqlDatabase> database,
                              const std::string& viewName,
                              const std::string& schema,
                              bool loadMetadata) {
    SqlView view(viewName);

    if (!schema.empty()) {
        view.setSchema(schema);
    }

    if (database) {
        view.setDatabase(database);

        if (loadMetadata) {
            view.refreshMetadata();
        }
    }

    return view;
}

//----------------------------------------------------------------------
// Helper Methods
//----------------------------------------------------------------------

/*SqlError SqlView::lastError() const {
    if (d_ptr) {
        return d_ptr->lastError;
    }
    return SqlError{};
}

void SqlView::clearError() {
    if (d_ptr) {
        d_ptr->clearError();
    }
}*/

} // namespace database 
