#ifndef DATABASE_DELETE_BUILDER_H
#define DATABASE_DELETE_BUILDER_H

#include <vector>
#include <string>
#include <optional>

#include "sql_builder.h"

namespace database {

/**
 * @brief Builder for SQL DELETE queries
 *
 * This class provides a fluent interface for building SQL DELETE queries
 * using object-based parameters. It supports specifying the table to delete from
 * and adding WHERE conditions.
 */
class DeleteBuilder : public SqlBuilder {
public:
    /**
     * @brief Constructor
     */
    DeleteBuilder();

    /**
     * @brief Constructor with table
     * @param table The table to delete from
     */
    explicit DeleteBuilder(const SqlTable& table);

    /**
     * @brief Specify the table to delete from
     * @param table The table
     * @return Reference to this builder for method chaining
     */
    DeleteBuilder& from(const SqlTable& table);

    /**
     * @brief Add a WHERE condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    DeleteBuilder& where(const SqlCondition& condition);

    /**
     * @brief Add an AND condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    DeleteBuilder& andWhere(const SqlCondition& condition);

    /**
     * @brief Add an OR condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    DeleteBuilder& orWhere(const SqlCondition& condition);

    /**
     * @brief Build the SQL query string
     * @return The SQL query string
     */
    [[nodiscard]] std::string build() const override;

private:
    // Helper method to add a where clause
    void addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator);

    // SqlTable information
    std::optional<SqlTable> m_table;

    // WHERE conditions
    struct WhereClause {
        SqlCondition condition;
        SqlLogicalOperator logicalOperator;
        bool isFirst;
    };
    std::vector<WhereClause> m_whereConditions;

    // Helper method to build the SQL query
    void buildSql() const;
};

} // namespace database

#endif // DATABASE_DELETE_BUILDER_H
