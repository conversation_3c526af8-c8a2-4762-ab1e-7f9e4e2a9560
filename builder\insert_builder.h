#ifndef DATABASE_INSERT_BUILDER_H
#define DATABASE_INSERT_BUILDER_H

#include <vector>
#include <string>
#include <unordered_map>
#include <optional>
#include <span>

#include "sql_builder.h"
#include "sql_row.h"

namespace database {

/**
 * @brief Builder for SQL INSERT queries
 *
 * This class provides a fluent interface for building SQL INSERT queries.
 * It supports inserting values into specific columns and batch inserts.
 */
class InsertBuilder : public SqlBuilder {
public:
    /**
     * @brief Constructor
     */
    InsertBuilder();

    /**
     * @brief Specify the table to insert into
     * @param table The table
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& into(const SqlTable& table);

    /**
     * @brief Specify the columns to insert into using variadic templates
     * @tparam Args Types of the columns (must be convertible to SqlColumn)
     * @param columns The columns to insert into
     * @return Reference to this builder for method chaining
     */
    template<ColumnConvertible... Args>
    InsertBuilder& columns(Args&&... cols) {
        m_columns.clear();
        if constexpr (sizeof...(cols) > 0) {
            (m_columns.push_back(SqlColumn(std::forward<Args>(cols))), ...);
        }
        m_sqlDirty = true;
        return *this;
    }

    /**
     * @brief Specify the columns to insert into
     * @param columns The columns
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& columns(std::span<const SqlColumn> columns);

    /**
     * @brief Specify the values to insert using variadic templates
     * @tparam Args Types of the values (must be convertible to Variant)
     * @param values The values to insert
     * @return Reference to this builder for method chaining
     */
    template<VariantConvertible... Args>
    InsertBuilder& values(Args&&... vals) {
        std::vector<Variant> valueVector;
        if constexpr (sizeof...(vals) > 0) {
            (valueVector.push_back(Variant(std::forward<Args>(vals))), ...);
        }

        m_useColumnValues = false;

        // Clear any existing batches and add this as the first batch
        m_valuesBatch.clear();
        m_valuesBatch.push_back(std::move(valueVector));

        m_sqlDirty = true;
        return *this;
    }

    /**
     * @brief Specify the values to insert
     * @param values The values to insert
     * @return Reference to this builder for method chaining
     */
    // InsertBuilder& values(const std::vector<Variant>& values);

    /**
     * @brief Set a column value
     * @param column The column
     * @param value The value to insert
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& set(const SqlColumn& column, const Variant& value);

    /**
     * @brief Set multiple column values using variadic templates
     * @tparam Args Types of the column-value pairs (must be convertible to std::pair<SqlColumn, Variant>)
     * @param columnValues The column-value pairs to set
     * @return Reference to this builder for method chaining
     */
    template<ColumnValuePair... Args>
    InsertBuilder& set(Args&&... columnValues) {
        if constexpr (sizeof...(columnValues) > 0) {
            m_useColumnValues = true;
            (addColumnValue(std::forward<Args>(columnValues)), ...);
            m_sqlDirty = true;
        }
        return *this;
    }

    /**
     * @brief Set values from a row
     * @param row The row with values to insert
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& setRow(const SqlRow& row);

    /**
     * @brief Add a batch of values for batch insert
     * @param values The values to insert
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& addBatch(const std::vector<Variant>& values);

    /**
     * @brief Add a batch of values for batch insert
     * @param values The values to insert
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& addBatch(std::span<const Variant> values);

    /**
     * @brief Add a row to the batch
     * @param row The row to add to the batch
     * @return Reference to this builder for method chaining
     */
    InsertBuilder& addBatchRow(const SqlRow& row);

    /**
     * @brief Build the SQL query string
     * @return The SQL query string
     */
    [[nodiscard]] std::string build() const override;

    /**
     * @brief Create a SqlQuery object from this builder
     * @param database The database to use for the query
     * @return A SqlQuery object with the SQL and parameters set
     */
    [[nodiscard]] SqlQuery toQuery(SqlDatabase& database) const override;

    /**
     * @brief Execute the query directly
     * @param database The database to use for the query
     * @return A SqlQuery object with the results
     */
    [[nodiscard]] SqlQuery execute(SqlDatabase& database) const override;

private:
    // Helper method to add a column-value pair
    void addColumnValue(const std::pair<SqlColumn, Variant>& columnValue) {
        m_columnValues[columnValue.first] = columnValue.second;
    }

    // SqlTable information
    std::optional<SqlTable> m_table;

    // SqlColumn information
    std::vector<SqlColumn> m_columns;

    // SqlColumn values for direct setting
    std::unordered_map<SqlColumn, Variant> m_columnValues;

    // Values for batch insert
    std::vector<std::vector<Variant>> m_valuesBatch;

    // Flag to indicate if we're using direct column values or batch values
    bool m_useColumnValues;

    // Helper method to build the SQL query
    void buildSql() const;
};

} // namespace database

#endif // DATABASE_INSERT_BUILDER_H
