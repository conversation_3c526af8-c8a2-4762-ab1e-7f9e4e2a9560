#include "sql_query.h"

#include "sql_database.h"

namespace database {

/**
 * @brief Private implementation class for SqlQuery
 *
 * This class provides the implementation details for SqlQuery.
 * It manages the statement and result objects.
 */
class SqlQueryPrivate {
public:
    SqlQueryPrivate() = default;
    ~SqlQueryPrivate() = default;

    // Database connection
    SqlDatabase* database = nullptr;

    // SQL query string
    std::string queryString;

    // Statement and result
    std::shared_ptr<SqlStatement> statement = nullptr;
    std::shared_ptr<SqlResult> result = nullptr;

    // Error information
    SqlError lastError;

    // State
    bool prepared = false;
    bool executed = false;
};

SqlQuery::SqlQuery()
    : d_ptr(std::make_unique<SqlQueryPrivate>())
{
}

SqlQuery::SqlQuery(SqlDatabase& database)
    : d_ptr(std::make_unique<SqlQueryPrivate>())
{
    d_ptr->database = &database;
}

SqlQuery::SqlQuery(SqlDatabase& database, std::string_view query)
    : d_ptr(std::make_unique<SqlQueryPrivate>())
{
    d_ptr->database = &database;
    d_ptr->queryString = std::string(query);
}

SqlQuery::SqlQuery(SqlQuery&& other) noexcept
    : d_ptr(std::move(other.d_ptr))
{
    // The moved-from object should be left in a valid but unspecified state
    // Create a new empty implementation for it
    other.d_ptr = std::make_unique<SqlQueryPrivate>();
}

SqlQuery& SqlQuery::operator=(SqlQuery&& other) noexcept {
    if (this != &other) {
        // Move the implementation
        d_ptr = std::move(other.d_ptr);

        // Create a new empty implementation for the moved-from object
        other.d_ptr = std::make_unique<SqlQueryPrivate>();
    }
    return *this;
}

SqlQuery::~SqlQuery() = default;

SqlQuery& SqlQuery::setDatabase(SqlDatabase& database) {
    // Reset state if database changes
    if (d_ptr->database != &database) {
        d_ptr->statement.reset();
        d_ptr->result.reset();
        d_ptr->prepared = false;
        d_ptr->executed = false;
    }

    d_ptr->database = &database;
    return *this;
}

SqlQuery& SqlQuery::setQuery(std::string_view query) {
    // Reset state if query changes
    std::string queryStr(query);
    if (d_ptr->queryString != queryStr) {
        d_ptr->statement.reset();
        d_ptr->result.reset();
        d_ptr->prepared = false;
        d_ptr->executed = false;
    }

    d_ptr->queryString = std::move(queryStr);
    return *this;
}

bool SqlQuery::prepare() {
    // Check if already prepared with the same query
    if (d_ptr->prepared && d_ptr->statement) {
        return true;
    }

    // Check if database is set
    if (!d_ptr->database) {
        d_ptr->lastError = SqlError("No database set", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if query is set
    if (d_ptr->queryString.empty()) {
        d_ptr->lastError = SqlError("No query set", ErrorCode::InvalidArgument);
        return false;
    }

    // Prepare the statement
    d_ptr->statement = d_ptr->database->prepareStatement(d_ptr->queryString);
    if (!d_ptr->statement) {
        d_ptr->lastError = d_ptr->database->lastError();
        return false;
    }

    d_ptr->lastError.clear();
    d_ptr->prepared = true;
    return true;
}

SqlQuery& SqlQuery::bind(int position, const Variant& value) {
    // Ensure statement is prepared
    if (!d_ptr->prepared) {
        if (!prepare()) {
            return *this;
        }
    }

    // Check if preparation was successful
    if (!d_ptr->statement) {
        return *this;
    }

    // Bind the parameter
    if (!d_ptr->statement->bindValue(position, value)) {
        d_ptr->lastError = d_ptr->statement->lastError();
    }

    return *this;
}

SqlQuery& SqlQuery::bind(std::string_view name, const Variant& value) {
    // Ensure statement is prepared
    if (!d_ptr->prepared) {
        if (!prepare()) {
            return *this;
        }
    }

    // Check if preparation was successful
    if (!d_ptr->statement) {
        return *this;
    }

    // Bind the parameter
    if (!d_ptr->statement->bindValue(name, value)) {
        d_ptr->lastError = d_ptr->statement->lastError();
    }

    return *this;
}

SqlQuery& SqlQuery::clearBindings() {
    if (d_ptr->statement) {
        d_ptr->statement->clearBindings();
    }

    return *this;
}

bool SqlQuery::execute() {
    // Ensure statement is prepared
    if (!d_ptr->prepared) {
        if (!prepare()) {
            return false;
        }
    }

    // Check if preparation was successful
    if (!d_ptr->statement) {
        return false;
    }

    // Execute the statement
    if (!d_ptr->statement->execute()) {
        d_ptr->lastError = d_ptr->statement->lastError();
        return false;
    }

    d_ptr->lastError.clear();

    // Get the result
    d_ptr->result = d_ptr->statement->result();
    d_ptr->executed = true;

    return true;
}

SqlQuery& SqlQuery::addBatch() {
    // Ensure statement is prepared
    if (!d_ptr->prepared) {
        if (!prepare()) {
            return *this;
        }
    }

    // Check if preparation was successful
    if (!d_ptr->statement) {
        return *this;
    }

    // Add batch
    if (!d_ptr->statement->addBatch()) {
        d_ptr->lastError = d_ptr->statement->lastError();
    }

    return *this;
}

bool SqlQuery::executeBatch() {
    // Ensure statement is prepared
    if (!d_ptr->prepared) {
        if (!prepare()) {
            return false;
        }
    }

    // Check if preparation was successful
    if (!d_ptr->statement) {
        return false;
    }

    // Execute batch
    if (!d_ptr->statement->executeBatch()) {
        d_ptr->lastError = d_ptr->statement->lastError();
        return false;
    }

    return true;
}

SqlQuery& SqlQuery::clearBatch() {
    if (d_ptr->statement) {
        d_ptr->statement->clearBatch();
    }

    return *this;
}

bool SqlQuery::next() {
    // Ensure query has been executed
    if (!d_ptr->executed) {
        if (!execute()) {
            return false;
        }
    }

    // Check if execution was successful
    if (!d_ptr->result) {
        return false;
    }

    return d_ptr->result->next();
}

bool SqlQuery::first() {
    // Ensure query has been executed
    if (!d_ptr->executed) {
        if (!execute()) {
            return false;
        }
    }

    // Check if execution was successful
    if (!d_ptr->result) {
        return false;
    }

    return d_ptr->result->first();
}

bool SqlQuery::last() {
    // Ensure query has been executed
    if (!d_ptr->executed) {
        if (!execute()) {
            return false;
        }
    }

    // Check if execution was successful
    if (!d_ptr->result) {
        return false;
    }

    return d_ptr->result->last();
}

bool SqlQuery::seek(int row) {
    // Ensure query has been executed
    if (!d_ptr->executed) {
        if (!execute()) {
            return false;
        }
    }

    // Check if execution was successful
    if (!d_ptr->result) {
        return false;
    }

    return d_ptr->result->fetch(row);
}

Variant SqlQuery::value(int index) const {
    if (!d_ptr->result) {
        return Variant();
    }

    return d_ptr->result->value(index);
}

Variant SqlQuery::value(std::string_view name) const {
    if (!d_ptr->result) {
        return Variant();
    }

    return d_ptr->result->value(name);
}

SqlRecord SqlQuery::record() const {
    if (!d_ptr->result) {
        return SqlRecord();
    }

    return d_ptr->result->record();
}

int SqlQuery::numRowsAffected() const {
    if (!d_ptr->statement) {
        return -1;
    }

    return d_ptr->statement->numRowsAffected();
}

int SqlQuery::rowCount() const {
    if (!d_ptr->result) {
        return -1;
    }

    return d_ptr->result->rowCount();
}

int SqlQuery::columnCount() const {
    if (!d_ptr->result) {
        return 0;
    }

    return d_ptr->result->columnCount();
}

std::vector<std::string> SqlQuery::columnNames() const {
    if (!d_ptr->result) {
        return {};
    }

    return d_ptr->result->columnNames();
}

bool SqlQuery::isValid() const {
    return d_ptr->database != nullptr && !d_ptr->queryString.empty();
}

bool SqlQuery::isActive() const {
    return d_ptr->executed && d_ptr->result && d_ptr->result->isActive();
}

const SqlError& SqlQuery::lastError() const {
    return d_ptr->lastError;
}

SqlQuery& SqlQuery::close() {
    if (d_ptr->statement) {
        d_ptr->statement->close();
    }

    d_ptr->result.reset();
    d_ptr->executed = false;

    return *this;
}

std::shared_ptr<SqlStatement> SqlQuery::statement() const {
    return d_ptr->statement;
}

std::shared_ptr<SqlResult> SqlQuery::result() const {
    return d_ptr->result;
}

} // namespace database
