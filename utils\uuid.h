#ifndef UUID_H
#define UUID_H

#include <array>
#include <random>
#include <ctime>

/**
 * @brief Simple UUID implementation
 */
class Uuid {
public:
    // UUID size in bytes
    static constexpr size_t UUID_SIZE = 16;
    
    // UUID array type
    using UuidArray = std::array<uint8_t, UUID_SIZE>;
    
    /**
     * @brief Create a new UUID
     * @return UUID array
     */
    static UuidArray createUuid() {
        UuidArray uuid{};
        
        // Use random number generator to create UUID
        static std::mt19937 rng(static_cast<unsigned int>(std::time(nullptr)));
        std::uniform_int_distribution<int> dist(0, 255);
        
        // Fill the array with random bytes
        for (size_t i = 0; i < UUID_SIZE; ++i) {
            uuid[i] = static_cast<uint8_t>(dist(rng));
        }
        
        // Set version (4) and variant (RFC 4122) bits
        uuid[6] = (uuid[6] & 0x0F) | 0x40;  // Version 4
        uuid[8] = (uuid[8] & 0x3F) | 0x80;  // Variant RFC 4122
        
        return uuid;
    }
};

#endif // UUID_H
