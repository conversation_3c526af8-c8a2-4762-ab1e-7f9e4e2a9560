#ifndef DATABASE_SQL_DATABASE_H
#define DATABASE_SQL_DATABASE_H

#include <memory>
#include <string>
#include <string_view>
#include <vector>
#include <functional>
#include <type_traits>

#include "driver/sql_statement.h"
#include "driver/sql_driver.h"
#include "exception/sql_error.h"

namespace database {

// Forward declarations
class SqlDriverCreatorBase;
class SqlDatabasePrivate;

/**
 * @brief Main database connection class
 *
 * This class represents a connection to a database and provides
 * the primary interface for users to interact with the database.
 * It wraps a SqlDriver implementation and delegates operations to it.
 *
 * It also provides static methods for managing database connections and drivers.
 */
class SqlDatabase {
public:
    /**
     * @brief Default constructor
     *
     * Creates an invalid database connection.
     */
    SqlDatabase();

    /**
     * @brief Constructor
     * @param driver The database driver
     * @param params The connection parameters
     * @throws SqlException if the driver is invalid
     */
    SqlDatabase(std::unique_ptr<SqlDriver> driver, const ConnectionParams& params);

    /**
     * @brief Destructor
     */
    ~SqlDatabase();

    /**
     * @brief Copy constructor
     */
    SqlDatabase(const SqlDatabase& other);

    /**
     * @brief Move constructor
     */
    SqlDatabase(SqlDatabase&&) noexcept;

    /**
     * @brief Copy assignment operator
     */
    SqlDatabase& operator=(const SqlDatabase& other);

    /**
     * @brief Move assignment operator
     */
    SqlDatabase& operator=(SqlDatabase&&) noexcept;

    /**
     * @brief Connect to the database
     * @return True if successful, false otherwise
     */
    bool connect();

    /**
     * @brief Disconnect from the database
     * @return True if successful, false otherwise
     */
    bool disconnect();

    /**
     * @brief Check if connected to the database
     * @return True if connected, false otherwise
     */
    [[nodiscard]] bool isConnected() const noexcept;

    [[nodiscard]] bool isOpen() const noexcept { return isConnected(); }

    /**
     * @brief Create a statement for executing SQL
     * @return A shared pointer to a statement
     */
    [[nodiscard]] std::shared_ptr<SqlStatement> createStatement();

    /**
     * @brief Create a prepared statement
     * @param sql The SQL query
     * @return A shared pointer to a statement
     */
    [[nodiscard]] std::shared_ptr<SqlStatement> prepareStatement(std::string_view sql);

    /**
     * @brief Begin a transaction
     * @param level The transaction isolation level
     * @return True if successful, false otherwise
     */
    bool transaction(TransactionIsolation level = TransactionIsolation::ReadCommitted);

    /**
     * @brief Commit the current transaction
     * @return True if successful, false otherwise
     */
    bool commit();

    /**
     * @brief Rollback the current transaction
     * @return True if successful, false otherwise
     */
    bool rollback();

    /**
     * @brief Check if a transaction is active
     * @return True if active, false otherwise
     */
    [[nodiscard]] bool isTransactionActive() const noexcept;

    /**
     * @brief Create a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    bool createSavepoint(std::string_view name);

    /**
     * @brief Rollback to a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    bool rollbackToSavepoint(std::string_view name);

    /**
     * @brief Release a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    bool releaseSavepoint(std::string_view name);

    /**
     * @brief Get the last error
     * @return The last error
     */
    [[nodiscard]] const SqlError& lastError() const noexcept;

    /**
     * @brief Get connection metadata
     * @return The connection metadata
     */
    [[nodiscard]] ConnectionMetadata metadata() const;

    /**
     * @brief Get the underlying database driver
     * @return A pointer to the database driver
     */
    [[nodiscard]] SqlDriver* driver() const noexcept;

    /**
     * @brief Get the connection parameters
     * @return The connection parameters
     */
    [[nodiscard]] const ConnectionParams& connectionParams() const noexcept;

    /**
     * @brief Check if the database connection is valid
     * @return True if valid, false otherwise
     */
    [[nodiscard]] bool isValid() const noexcept;

    /**
     * @brief Get the driver name
     * @return The driver name
     */
    [[nodiscard]] std::string_view driverName() const;

    /**
     * @brief Get the database name
     * @return The database name
     */
    [[nodiscard]] std::string_view databaseName() const;

    /**
     * @brief Get the user name
     * @return The user name
     */
    [[nodiscard]] std::string_view userName() const;

    /**
     * @brief Get the password
     * @return The password
     */
    [[nodiscard]] std::string_view password() const;

    /**
     * @brief Get the host name
     * @return The host name
     */
    [[nodiscard]] std::string_view hostName() const;

    /**
     * @brief Get the port
     * @return The port
     */
    [[nodiscard]] int port() const noexcept;

    /**
     * @brief Get the connection options
     * @return The connection options
     */
    [[nodiscard]] std::string connectOptions() const;

    /**
     * @brief Get the connection name
     * @return The connection name
     */
    [[nodiscard]] std::string_view connectionName() const;

    /**
     * @brief Set the database name
     * @param name The database name
     */
    void setDatabaseName(std::string_view name);

    /**
     * @brief Set the user name
     * @param name The user name
     */
    void setUserName(std::string_view name);

    /**
     * @brief Set the password
     * @param password The password
     */
    void setPassword(std::string_view password);

    /**
     * @brief Set the host name
     * @param host The host name
     */
    void setHostName(std::string_view host);

    /**
     * @brief Set the port
     * @param port The port
     */
    void setPort(int port) noexcept;

    /**
     * @brief Set the connection options
     * @param options The connection options
     */
    void setConnectOptions(std::string_view options = "");

    // Static methods for database management

    /**
     * @brief Add a database connection
     * @param type The driver type
     * @param connectionName The connection name (optional)
     * @return The database connection
     */
    [[nodiscard]] static SqlDatabase addDatabase(std::string_view type, std::string_view connectionName = "default");

    /**
     * @brief Add a database connection with an existing driver
     * @param driver The database driver
     * @param connectionName The connection name (optional)
     * @return The database connection
     */
    [[nodiscard]] static SqlDatabase addDatabase(std::unique_ptr<SqlDriver> driver, std::string_view connectionName = "default");

    /**
     * @brief Clone a database connection
     * @param other The database connection to clone
     * @param connectionName The new connection name
     * @return The cloned database connection
     */
    [[nodiscard]] static SqlDatabase cloneDatabase(const SqlDatabase& other, std::string_view connectionName);

    /**
     * @brief Get a database connection by name
     * @param connectionName The connection name (optional)
     * @param open Whether to open the connection (optional)
     * @return The database connection
     */
    [[nodiscard]] static SqlDatabase database(std::string_view connectionName = "default", bool open = true);

    /**
     * @brief Remove a database connection
     * @param connectionName The connection name
     */
    static void removeDatabase(std::string_view connectionName);

    /**
     * @brief Check if a database connection exists
     * @param connectionName The connection name (optional)
     * @return True if the connection exists, false otherwise
     */
    [[nodiscard]] static bool contains(std::string_view connectionName = "default");

    /**
     * @brief Get the list of available drivers
     * @return The list of driver names
     */
    [[nodiscard]] static std::vector<std::string> drivers();

    /**
     * @brief Get the list of connection names
     * @return The list of connection names
     */
    [[nodiscard]] static std::vector<std::string> connectionNames();

    /**
     * @brief Register a SQL driver
     * @param name The driver name
     * @param creator The driver creator
     */
    static void registerSqlDriver(std::string_view name, std::shared_ptr<SqlDriverCreatorBase> creator);

    /**
     * @brief Register a SQL driver with a factory function
     * @param name The driver name
     * @param factory The factory function
     */
    static void registerSqlDriver(std::string_view name, std::function<std::unique_ptr<SqlDriver>()> factory);

    /**
     * @brief Check if a driver is available
     * @param name The driver name
     * @return True if available, false otherwise
     */
    [[nodiscard]] static bool isDriverAvailable(std::string_view name);

protected:
    /**
     * @brief Constructor with driver type
     * @param type The driver type
     */
    explicit SqlDatabase(std::string_view type);

private:
    friend class SqlDatabasePrivate;

    std::shared_ptr<SqlDatabasePrivate> d_ptr;
};

/**
 * @brief Base class for SQL driver creators
 *
 * This class defines the interface for creating SQL drivers.
 */
class SqlDriverCreatorBase {
public:
    virtual ~SqlDriverCreatorBase() = default;

    /**
     * @brief Create a new driver instance
     * @return A unique pointer to the driver
     */
    virtual std::unique_ptr<SqlDriver> createDriver() const = 0;
};

/**
 * @brief Template class for creating SQL drivers
 *
 * This class provides a concrete implementation of SqlDriverCreatorBase
 * for creating drivers of a specific type.
 *
 * @tparam T The driver type, must be derived from SqlDriver
 */
template <typename T>
    requires std::derived_from<T, SqlDriver>
class SqlDriverCreator : public SqlDriverCreatorBase {
public:
    [[nodiscard]] std::unique_ptr<SqlDriver> createDriver() const override {
        return std::make_unique<T>();
    }
};

} // namespace database

#endif // DATABASE_SQL_DATABASE_H
