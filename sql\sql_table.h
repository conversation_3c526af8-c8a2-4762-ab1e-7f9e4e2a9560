#ifndef DATABASE_SQL_TABLE_H
#define DATABASE_SQL_TABLE_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <optional>
#include <chrono>
#include <span>
#include <ranges>
#include <algorithm>
#include <type_traits>
#include <unordered_map>

#include "sql_database.h"
#include "sql_enums.h"
#include "sql_index.h"
#include "sql_object.h"

namespace database {

// Forward declarations
class SqlTablePrivate;
class SqlColumn;
class SqlIndex;
class SqlRow;

/**
 * @brief Type trait for table-like objects
 */
template<typename T>
struct is_table_like {
    template<typename U>
    static auto test(int) -> decltype(
        std::declval<const U&>().schema(),
        std::declval<const U&>().columns(),
        std::declval<const U&>().rowCount(),
        std::true_type{}
    );

    template<typename>
    static std::false_type test(...);

    static constexpr bool value = decltype(test<T>(0))::value;
};

template<typename T>
constexpr bool is_table_like_v = is_table_like<T>::value;

/**
 * @brief Class representing a database table
 *
 * This class operates in two distinct modes:
 * 1. Builder Mode: Lightweight SQL statement construction without database access
 * 2. Metadata Mode: Full database metadata access via driver delegation
 *
 */
class SqlTable final : public SqlObject {
public:
    /**
     * @brief Enhanced table metadata structure with modern C++ optimizations
     */
    struct SqlTableMetadata {
        std::string schema;                                 ///< Schema name
        std::string engine;                                 ///< Storage engine (e.g., InnoDB, MyISAM)
        std::string catalog;                                ///< Catalog name
        std::string comment;                                ///< Table comment/description
        std::string charset;                                ///< Character set
        std::string collation;                              ///< Collation

        std::optional<size_t> rowCount;                                    ///< Estimated row count
        std::optional<size_t> dataLength;                                  ///< Data length in bytes
        std::optional<size_t> indexLength;                                 ///< Index length in bytes
        std::optional<size_t> autoIncrementValue;                          ///< Next auto increment value
        std::vector<std::string> partitionKeys;                            ///< Partition keys
        bool isTemporary = false;                                          ///< Is temporary table
        bool isView = false;                                               ///< Is view (materialized or regular)

        std::optional<std::chrono::system_clock::time_point> createdTime;  ///< Creation time
        std::optional<std::chrono::system_clock::time_point> modifiedTime; ///< Last modification time

        std::unordered_map<std::string, std::string> properties;           ///< Additional properties

        std::vector<SqlColumn> columns;                                     ///< Table columns
        std::unordered_map<std::string, size_t> columnMap;                  ///< Maps column names to indices
        std::vector<SqlIndex> indexes;                                      ///< Table indexes

        // Enhanced constructors with perfect forwarding
        SqlTableMetadata() = default;

        template<typename Schema, typename Comment = std::string>
        SqlTableMetadata(Schema&& schema, Comment&& comment = {})
            : schema(std::forward<Schema>(schema))
            , comment(std::forward<Comment>(comment)) {}

        // Enhanced column management with modern C++ features
        template<typename ColumnRange>
        void setColumns(ColumnRange&& columnRange) {
            columns.clear();
            columnMap.clear();

            // Reserve space if possible
            if constexpr (std::is_same_v<std::decay_t<ColumnRange>, std::vector<SqlColumn>>) {
                columns.reserve(columnRange.size());
            }

            size_t index = 0;
            for (auto&& column : columnRange) {
                columns.emplace_back(std::forward<decltype(column)>(column));
                columnMap[std::string(columns.back().name())] = index++;
            }
        }

        // Utility methods for column access
        [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept {
            return columnMap.find(std::string(columnName)) != columnMap.end();
        }

        [[nodiscard]] const SqlColumn* findColumn(std::string_view columnName) const noexcept {
            auto it = columnMap.find(std::string(columnName));
            return (it != columnMap.end() && it->second < columns.size())
                   ? &columns[it->second] : nullptr;
        }

        [[nodiscard]] SqlColumn* findColumn(std::string_view columnName) noexcept {
            auto it = columnMap.find(std::string(columnName));
            return (it != columnMap.end() && it->second < columns.size())
                   ? &columns[it->second] : nullptr;
        }

        // Index management utilities
        [[nodiscard]] bool hasIndex(std::string_view indexName) const noexcept {
            return std::any_of(indexes.begin(), indexes.end(), [indexName](const auto& idx) {
                return idx.name() == indexName;
            });
        }

        [[nodiscard]] const SqlIndex* findIndex(std::string_view indexName) const noexcept {
            auto it = std::find_if(indexes.begin(), indexes.end(), [indexName](const auto& idx) {
                return idx.name() == indexName;
            });
            return it != indexes.end() ? &(*it) : nullptr;
        }
    };

    // Enhanced constructors with modern C++ features
    SqlTable() noexcept;
    explicit SqlTable(std::string_view name) noexcept;
    SqlTable(std::string_view name, std::shared_ptr<SqlDatabase> db);
    SqlTable(std::string_view name, std::string_view schema, std::string_view alias = {}) noexcept;

    // Template constructor for type-safe table creation with columns
    template<typename ColumnRange>
    SqlTable(std::string_view name, ColumnRange&& columns)
        : SqlTable(name) {
        if (auto meta = metadata()) {
            meta->setColumns(std::forward<ColumnRange>(columns));
        }
    }

    ~SqlTable();

    // Enhanced copy/move operations with optimizations
    SqlTable(const SqlTable& other);
    SqlTable& operator=(const SqlTable& other);
    SqlTable(SqlTable&& other) noexcept;
    SqlTable& operator=(SqlTable&& other) noexcept;

    //----------------------------------------------------------------------
    // Enhanced Static Factory Methods
    //----------------------------------------------------------------------
    [[nodiscard]] static SqlTable fromDatabase(std::string_view name, std::shared_ptr<SqlDatabase> db);

    [[nodiscard]] static std::vector<SqlTable> allTables(std::shared_ptr<SqlDatabase> db,
                                                         std::string_view schema = "",
                                                         SqlObjectType type = SqlObjectType::Table);

    // Modern factory methods with perfect forwarding
    template<typename... Args>
    [[nodiscard]] static SqlTable create(std::string_view name, Args&&... args) {
        SqlTable table(name);
        if constexpr (sizeof...(args) > 0) {
            auto meta = table.metadata();
            if (meta) {
                // Apply additional metadata using fold expressions
                (table.applyMetadataArg(std::forward<Args>(args)), ...);
            }
        }
        return table;
    }

    // Utility factory methods for common table types
    [[nodiscard]] static SqlTable temporary(std::string_view name) {
        auto table = SqlTable(name);
        if (table.metadata()) {
            table.metadata()->isTemporary = true;
        }
        return table;
    }

    //----------------------------------------------------------------------
    // Enhanced Metadata Management with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] SqlTableMetadata* metadata() const noexcept;
    SqlTable& setMetadata(const SqlTableMetadata& metadata);
    [[nodiscard]] bool hasMetadata() const noexcept override;
    bool loadMetadata() override;
    bool refreshMetadata() override;

    // Core properties with optimized accessors and fluent interface
    [[nodiscard]] std::string_view schema() const noexcept;
    SqlTable& setSchema(std::string_view schema);

    [[nodiscard]] std::string_view engine() const noexcept;
    SqlTable& setEngine(std::string_view engine);

    [[nodiscard]] std::string_view catalog() const noexcept;
    SqlTable& setCatalog(std::string_view catalog);

    [[nodiscard]] std::string_view comment() const noexcept;
    SqlTable& setComment(std::string_view comment);

    [[nodiscard]] std::string_view charset() const noexcept;
    SqlTable& setCharset(std::string_view charset);

    [[nodiscard]] std::string_view collation() const noexcept;
    SqlTable& setCollation(std::string_view collation);

    // Boolean properties with fluent interface
    [[nodiscard]] bool isTemporary() const noexcept;
    SqlTable& setTemporary(bool temporary = true) noexcept;
    SqlTable& temporary() noexcept { return setTemporary(true); }

    [[nodiscard]] bool isView() const noexcept;
    SqlTable& setView(bool view = true) noexcept;

    //----------------------------------------------------------------------
    // Enhanced Column Management with Modern C++ Features
    //----------------------------------------------------------------------
    [[nodiscard]] std::vector<SqlColumn> columns() const noexcept;
    [[nodiscard]] std::span<const SqlColumn> columnsSpan() const noexcept;

    [[nodiscard]] SqlColumn column(std::string_view columnName);
    [[nodiscard]] SqlColumn column(std::string_view columnName) const;

    // Template-based column access with type safety
    template<typename T = SqlColumn>
    [[nodiscard]] std::optional<T> findColumn(std::string_view columnName) const noexcept {
        static_assert(std::is_same_v<T, SqlColumn> || std::is_base_of_v<SqlColumn, T>,
                     "T must be SqlColumn or derived from SqlColumn");

        if (auto meta = metadata()) {
            if (auto* col = meta->findColumn(columnName)) {
                if constexpr (std::is_same_v<T, SqlColumn>) {
                    return *col;
                } else {
                    return T(*col);
                }
            }
        }
        return std::nullopt;
    }

    // Enhanced column modification with fluent interface
    SqlTable& addColumn(const SqlColumn& column);
    SqlTable& addColumn(SqlColumn&& column);

    template<typename... Args>
    SqlTable& addColumn(std::string_view name, SqlDataType type, Args&&... args) {
        auto column = SqlColumn::create(name, type, std::forward<Args>(args)...);
        return addColumn(std::move(column));
    }

    SqlTable& dropColumn(std::string_view columnName);
    SqlTable& modifyColumn(const SqlColumn& column);
    SqlTable& modifyColumn(SqlColumn&& column);

    // Enhanced column queries
    [[nodiscard]] bool hasColumn(std::string_view columnName) const noexcept;
    [[nodiscard]] size_t columnCount() const noexcept;

    // Modern column filtering with predicates
    template<typename Predicate>
    [[nodiscard]] std::vector<SqlColumn> filterColumns(Predicate&& pred) const {
        auto cols = columns();
        std::vector<SqlColumn> result;
        std::copy_if(cols.begin(), cols.end(), std::back_inserter(result),
                    std::forward<Predicate>(pred));
        return result;
    }

    [[nodiscard]] std::vector<SqlColumn> primaryKeyColumns() const;
    [[nodiscard]] std::vector<SqlColumn> foreignKeyColumns() const;
    [[nodiscard]] std::vector<SqlColumn> uniqueColumns() const;

    //----------------------------------------------------------------------
    // Index Management
    //----------------------------------------------------------------------
    [[nodiscard]] std::vector<SqlIndex> indexes() const;
    [[nodiscard]] SqlIndex index(std::string_view indexName) const;
    [[nodiscard]] bool hasIndex(std::string_view indexName) const;

    bool addIndex(const SqlIndex& index);
    bool addIndex(std::string_view indexName, const std::vector<std::string>& columnNames, bool unique = false);
    bool dropIndex(std::string_view indexName);

    //----------------------------------------------------------------------
    // Row Management
    //----------------------------------------------------------------------
    [[nodiscard]] size_t rowCount() const;
    [[nodiscard]] std::vector<SqlRow> selectAll() const;
    [[nodiscard]] std::vector<SqlRow> select(const std::string& whereClause,
                                             const std::vector<Data>& parameters = {}) const;
    bool insert(const SqlRow& row);
    size_t insert(const std::vector<SqlRow>& rows);
    size_t update(const SqlRow& row, const std::string& whereClause, const std::vector<Data>& parameters = {});
    size_t deleteRows(const std::string& whereClause, const std::vector<Data>& parameters = {});

    //----------------------------------------------------------------------
    // Enhanced Database Interaction Operations
    //----------------------------------------------------------------------
    [[nodiscard]] std::shared_ptr<SqlDatabase> database() const override;
    SqlTable& setDatabase(std::shared_ptr<SqlDatabase> database) override;

    // Database operations with enhanced error handling
    [[nodiscard]] bool exists() const;
    bool create(const std::vector<SqlColumn>& columns, bool ifNotExists = true);
    bool create(bool ifNotExists = true);  // Create with existing metadata
    bool drop(bool ifExists = true);
    bool truncate();
    bool rename(std::string_view newName);

    // Enhanced SQL execution
    bool execute(const std::string& sql, const std::vector<Data>& parameters = {});
    [[nodiscard]] bool isEmpty() const;

    // Statistics and analysis
    [[nodiscard]] size_t estimatedRowCount() const;
    [[nodiscard]] size_t actualRowCount() const;
    [[nodiscard]] size_t dataSize() const;
    [[nodiscard]] size_t indexSize() const;

    //----------------------------------------------------------------------
    // Enhanced SqlObject Implementation
    //----------------------------------------------------------------------
    [[nodiscard]] std::string qualifiedName() const override;
    [[nodiscard]] std::string toSql() const override;

    // Enhanced SQL generation methods
    [[nodiscard]] std::string createSql(bool ifNotExists = false) const;
    [[nodiscard]] std::string alterSql() const;
    [[nodiscard]] std::string dropSql(bool ifExists = false) const;
    [[nodiscard]] std::string truncateSql() const;
    [[nodiscard]] std::string renameSql(std::string_view newName) const;

private:
    // Helper methods for metadata management
    template<typename T>
    void applyMetadataArg(T&& arg) {
        if constexpr (std::is_same_v<std::decay_t<T>, std::string>) {
            setSchema(std::forward<T>(arg));
        } else if constexpr (std::is_same_v<std::decay_t<T>, bool>) {
            setTemporary(std::forward<T>(arg));
        }
        // Add more metadata argument types as needed
    }

    // Enhanced member variables with better encapsulation
    mutable std::shared_ptr<SqlTableMetadata> m_metadata;
    mutable std::shared_ptr<SqlTablePrivate> d_ptr;
};

} // namespace database

// Enhanced hash specialization for SqlTable with modern C++ features
namespace std {
template<>
struct hash<database::SqlTable> {
    [[nodiscard]] size_t operator()(const database::SqlTable& table) const noexcept {
        // Use the base SqlObject hash combined with schema information
        size_t h1 = table.hash();

        // Hash the schema name if available
        size_t h2 = 0;
        try {
            auto schema = table.schema();
            if (!schema.empty()) {
                h2 = std::hash<std::string_view>{}(schema);
            }
        } catch (...) {
            // Ignore errors during hash calculation
        }

        // Hash the column count for additional uniqueness
        size_t h3 = table.columnCount();

        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};
} // namespace std

#endif // DATABASE_SQL_TABLE_H
