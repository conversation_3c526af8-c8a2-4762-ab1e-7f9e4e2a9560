#ifndef DATABASE_CONNECTION_POOL_MANAGER_H
#define DATABASE_CONNECTION_POOL_MANAGER_H

#include "connection_pool.h"
#include <memory>
#include <string>
#include <unordered_map>
#include <mutex>

namespace database {

/**
 * @brief Manager for connection pools
 *
 * This class manages multiple connection pools for different database connections.
 */
class ConnectionPoolManager {
public:
    /**
     * @brief Get the singleton instance
     * @return Reference to the singleton instance
     */
    static ConnectionPoolManager& instance();

    /**
     * @brief Create a connection pool
     * @param poolName The pool name
     * @param driverName The driver name
     * @param params The connection parameters
     * @param initialSize The initial pool size
     * @param maxSize The maximum pool size
     * @param timeout The connection timeout in milliseconds
     * @return A shared pointer to the connection pool
     */
    std::shared_ptr<ConnectionPool> createPool(
        const std::string& poolName,
        const std::string& driverName,
        const ConnectionParams& params,
        size_t initialSize = 5,
        size_t maxSize = 10,
        std::chrono::milliseconds timeout = std::chrono::seconds(30));

    /**
     * @brief Get a connection pool
     * @param poolName The pool name
     * @return A shared pointer to the connection pool, or nullptr if not found
     */
    std::shared_ptr<ConnectionPool> getPool(const std::string& poolName);

    /**
     * @brief Remove a connection pool
     * @param poolName The pool name
     * @return True if successful, false otherwise
     */
    bool removePool(const std::string& poolName);

    /**
     * @brief Get a connection from a pool
     * @param poolName The pool name
     * @return A shared pointer to a database connection
     */
    std::shared_ptr<SqlDatabase> getConnection(const std::string& poolName);

    /**
     * @brief Release a connection back to its pool
     * @param connection The connection to release
     * @param poolName The pool name
     */
    void releaseConnection(std::shared_ptr<SqlDatabase> connection, const std::string& poolName);

    /**
     * @brief Close all connection pools
     */
    void closeAll();

private:
    ConnectionPoolManager() = default;
    ~ConnectionPoolManager() = default;

    // Delete copy constructor and assignment operator
    ConnectionPoolManager(const ConnectionPoolManager&) = delete;
    ConnectionPoolManager& operator=(const ConnectionPoolManager&) = delete;

    std::unordered_map<std::string, std::shared_ptr<ConnectionPool>> m_pools;
    mutable std::mutex m_mutex;
};

} // namespace database

#endif // DATABASE_CONNECTION_POOL_MANAGER_H
